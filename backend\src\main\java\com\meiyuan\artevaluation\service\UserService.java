package com.meiyuan.artevaluation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.User;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 根据ID获取用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    User getUserById(Long id);

    /**
     * 根据openid获取用户
     *
     * @param openid 微信openid
     * @return 用户信息
     */
    User getUserByOpenid(String openid);

    /**
     * 创建或更新用户
     *
     * @param user 用户信息
     * @return 用户信息
     */
    User createOrUpdateUser(User user);

    /**
     * 获取用户角色
     *
     * @param userId 用户ID
     * @return 角色：1-学生，2-教师
     */
    Integer getUserRole(Long userId);

    /**
     * 获取用户总数
     *
     * @return 用户总数
     */
    long count();

    /**
     * 分页获取所有用户
     *
     * @param page 分页参数
     * @return 用户列表
     */
    IPage<User> getAllUsers(Page<User> page);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteUser(Long userId);

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUser(User user);
}