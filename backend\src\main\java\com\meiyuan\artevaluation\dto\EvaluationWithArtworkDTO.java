package com.meiyuan.artevaluation.dto;

import com.meiyuan.artevaluation.entity.Evaluation;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评价与作品信息DTO
 */
@Data
public class EvaluationWithArtworkDTO {
    
    // 评价信息
    private Long id;
    private Long artworkId;
    private Integer type; // 1-AI评价，2-教师评价
    private String technicalAnalysis;
    private String problemDiagnosis;
    private String improvementPlan;
    private String stageGoals;
    private String referenceMaterials;
    private String scores; // JSON格式的评分
    private LocalDateTime createTime;
    
    // 作品信息
    private String artworkTitle;
    private String artworkCover;
    private String artworkDescription;
    
    public static EvaluationWithArtworkDTO fromEvaluation(Evaluation evaluation) {
        EvaluationWithArtworkDTO dto = new EvaluationWithArtworkDTO();
        dto.setId(evaluation.getId());
        dto.setArtworkId(evaluation.getArtworkId());
        dto.setType(evaluation.getType());
        dto.setTechnicalAnalysis(evaluation.getTechnicalAnalysis());
        dto.setProblemDiagnosis(evaluation.getProblemDiagnosis());
        dto.setImprovementPlan(evaluation.getImprovementPlan());
        dto.setStageGoals(evaluation.getStageGoals());
        dto.setReferenceMaterials(evaluation.getReferenceMaterials());
        dto.setScores(evaluation.getScores());
        dto.setCreateTime(evaluation.getCreatedAt());
        return dto;
    }
}
