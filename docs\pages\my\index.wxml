<t-toast id="t-toast" />

<view class="my">
  <image class="nav-bg" src="/static/bg_navbar.png" />
  <nav title-text="我的" />
  <view class="my-info">
    <t-cell-group>
      <t-cell wx:if="{{isLoad}}" bordered="{{false}}">
        <t-avatar image="{{personalInfo.image}}" slot="left-icon" size="large" />
        <view class="my-info__person" slot="description">
          <view class="name">{{personalInfo.name}}</view>
          <view class="tags">
            <t-tag class="tag" variant="light" icon="discount">{{personalInfo.star}}</t-tag>
            <t-tag class="tag" variant="light" icon="location">{{personalInfo.city}}</t-tag>
          </view>
        </view>
        <t-icon slot="right-icon" name="edit" size="40rpx" color="#000000e6" bindtap="onNavigateTo" />
      </t-cell>
      <t-cell
        wx:if="{{!isLoad}}"
        title="请先登录/注册"
        bordered="{{false}}"
        t-class-title="cell-class-title"
        t-class-center="cell-class-center"
        bindtap="onLogin"
      >
        <t-avatar slot="left-icon" icon="user" size="128rpx" />
      </t-cell>
    </t-cell-group>
    <t-divider t-class="divider-class" />

    <!-- 用户统计信息 -->
    <view wx:if="{{isLoad}}" class="user-stats">
      <view class="stats-item">
        <text class="stats-number">{{userStats.totalDays || 0}}</text>
        <text class="stats-label">学习天数</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.totalUploads}}</text>
        <text class="stats-label">上传作品</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.totalEvaluations}}</text>
        <text class="stats-label">获得评价</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.averageScore}}</text>
        <text class="stats-label">平均分</text>
      </view>
    </view>

    <t-grid t-class="grid-class">
      <t-grid-item
        wx:for="{{gridList}}"
        wx:for-item="gridItem"
        wx:key="type"
        text="{{gridItem.name}}"
        icon="{{gridItem.icon}}"
        class="grid-item"
        data-data="{{gridItem}}"
        bind:click="onEleClick"
      />
    </t-grid>
  </view>

  <view class="my-service">
    <view class="my-service--tips">快速入口</view>
    <view class="quick-actions">
      <view class="action-item" bindtap="goUpload">
        <view class="action-icon">
          <t-icon name="upload" size="48rpx" color="#07c160" />
        </view>
        <text class="action-text">上传作品</text>
      </view>
      <view class="action-item" bindtap="goEvaluations">
        <view class="action-icon">
          <t-icon name="chart-bubble" size="48rpx" color="#ff6b35" />
        </view>
        <text class="action-text">查看评价</text>
      </view>
      <view class="action-item" bindtap="goTeacherCertification">
        <view class="action-icon">
          <t-icon name="user-avatar" size="48rpx" color="#0052d9" />
        </view>
        <text class="action-text">{{teacherCertificationText}}</text>
      </view>
    </view>
  </view>

  <t-cell-group theme="card">
    <t-cell
      wx:for="{{settingList}}"
      wx:for-item="item"
      wx:key="type"
      title="{{item.name}}"
      url="{{item.url}}"
      leftIcon="{{item.icon}}"
      hover
      arrow
      data-data="{{item}}"
      bind:click="onEleClick"
    />
  </t-cell-group>
</view>
