server:
  port: 8080

spring:
  application:
    name: art-evaluation-system
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************
    username: root
    password: '0110'
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
  thymeleaf:
    cache: true
    mode: HTML
    encoding: UTF-8
  
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 微信小程序配置
wechat:
  miniapp:
    app-id: ${WECHAT_APPID:wx33a5eb34e3b685fc}
    app-secret: ${WECHAT_SECRET:9edd1e97f3faf268eaf487d66b0e9ba9}

# 本地文件存储配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:E:/project_work/fhwl/wx_meiyuan_project/wx_meiyuan_project/backend/uploads}
    url-prefix: ${FILE_URL_PREFIX:http://127.0.0.1:8080/files/}
    allowed-types: jpg,jpeg,png,webp

# 火山方舟AI配置
volcengine:
  ark:
    api-key: ${ARK_API_KEY:905fab5e-33fa-4641-90b9-ed0bd6c0b6bc}
    base-url: ${ARK_BASE_URL:https://ark.cn-beijing.volces.com/api/v3}
    model: ${ARK_MODEL:deepseek-r1-250120}


# 日志配置
logging:
  level:
    root: INFO
    com.meiyuan.artevaluation: INFO
    org.springframework.web: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:/www/app/logs/art-evaluation.log}
    max-size: 100MB
    max-history: 30