package com.meiyuan.artevaluation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.TeacherApplication;
import com.meiyuan.artevaluation.entity.User;
import com.meiyuan.artevaluation.mapper.TeacherApplicationMapper;
import com.meiyuan.artevaluation.service.TeacherApplicationService;
import com.meiyuan.artevaluation.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 教师认证申请服务实现类
 */
@Slf4j
@Service
public class TeacherApplicationServiceImpl implements TeacherApplicationService {

    @Autowired
    private TeacherApplicationMapper teacherApplicationMapper;

    @Autowired
    private UserService userService;

    @Override
    @Transactional
    public TeacherApplication submitApplication(TeacherApplication application) {
        // 检查用户是否已经是教师
        User user = userService.getUserById(application.getUserId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        if (user.getRole() != null && user.getRole() == 2) {
            throw new RuntimeException("您已经是教师，无需重复申请");
        }

        // 检查是否有待审核的申请
        TeacherApplication existingApplication = getLatestByUserId(application.getUserId());
        if (existingApplication != null && existingApplication.getStatus() == 0) {
            throw new RuntimeException("您已有待审核的申请，请耐心等待");
        }

        // 设置默认状态
        application.setStatus(TeacherApplication.Status.PENDING.getCode());
        application.setCreatedAt(LocalDateTime.now());
        application.setUpdatedAt(LocalDateTime.now());

        // 保存申请
        teacherApplicationMapper.insert(application);
        log.info("用户 {} 提交教师认证申请，申请ID: {}", application.getUserId(), application.getId());

        return application;
    }

    @Override
    public TeacherApplication getLatestByUserId(Long userId) {
        return teacherApplicationMapper.getLatestByUserId(userId);
    }

    @Override
    public IPage<TeacherApplication> getApplicationsPage(Page<TeacherApplication> page, Integer status) {
        return teacherApplicationMapper.getApplicationsPage(page, status);
    }

    @Override
    @Transactional
    public boolean reviewApplication(Long applicationId, Integer status, String adminRemark, Long reviewerId) {
        TeacherApplication application = teacherApplicationMapper.selectById(applicationId);
        if (application == null) {
            throw new RuntimeException("申请记录不存在");
        }

        if (application.getStatus() != 0) {
            throw new RuntimeException("该申请已经审核过了");
        }

        // 更新申请状态
        application.setStatus(status);
        application.setAdminRemark(adminRemark);
        application.setReviewedAt(LocalDateTime.now());
        application.setReviewedBy(reviewerId);
        application.setUpdatedAt(LocalDateTime.now());

        int result = teacherApplicationMapper.updateById(application);

        // 如果审核通过，更新用户角色为教师
        if (result > 0 && status == 1) {
            User user = userService.getUserById(application.getUserId());
            if (user != null) {
                user.setRole(2); // 设置为教师角色
                userService.updateUser(user);
                log.info("用户 {} 教师认证审核通过，角色已更新为教师", application.getUserId());
            }
        }

        log.info("教师认证申请 {} 审核完成，状态: {}, 审核人: {}", applicationId, status, reviewerId);
        return result > 0;
    }

    @Override
    public TeacherApplication getById(Long id) {
        return teacherApplicationMapper.selectById(id);
    }

    @Override
    public Map<String, Object> getStatusStatistics() {
        return teacherApplicationMapper.getStatusStatistics();
    }

    @Override
    public boolean canApply(Long userId) {
        // 检查用户是否已经是教师
        User user = userService.getUserById(userId);
        if (user == null) {
            return false;
        }
        if (user.getRole() != null && user.getRole() == 2) {
            return false; // 已经是教师
        }

        // 检查是否有待审核的申请
        TeacherApplication existingApplication = getLatestByUserId(userId);
        if (existingApplication != null && existingApplication.getStatus() == 0) {
            return false; // 有待审核的申请
        }

        return true;
    }
}
