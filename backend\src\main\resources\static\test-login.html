<!DOCTYPE html>
<html>
<head>
    <title>登录测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>登录测试页面</h1>
    <button id="loginBtn">测试登录</button>
    <div id="result"></div>

    <script>
        $('#loginBtn').click(function() {
            $.ajax({
                url: '/api/auth/login',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({code: 'test123'}),
                success: function(response) {
                    $('#result').html('<pre>' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#result').html('错误: ' + error + '<br>' + xhr.responseText);
                }
            });
        });
    </script>
</body>
</html>
