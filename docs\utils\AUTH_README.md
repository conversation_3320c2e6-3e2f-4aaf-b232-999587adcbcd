# 登录状态管理系统

## 概述

本系统提供了完整的用户登录状态管理功能，包括：
- 全局登录状态管理
- 自动token过期处理
- 统一的登录检查机制
- 便捷的工具函数

## 核心功能

### 1. 全局状态管理 (app.js)

在 `app.js` 中提供了以下全局方法：

- `checkLoginStatus()` - 检查并更新登录状态
- `setLoginStatus(userInfo, token)` - 设置登录状态
- `logout()` - 退出登录
- `requireLogin()` - 检查是否需要登录

### 2. 自动token过期处理 (api/request.js)

API请求时自动检查响应状态码：
- 401/403 状态码自动触发退出登录
- 业务错误码401/403也会触发退出登录

### 3. 认证工具函数 (utils/auth.js)

提供便捷的认证相关工具函数：

```javascript
import { isLoggedIn, getCurrentUser, requireLogin } from '~/utils/auth';

// 检查是否已登录
if (isLoggedIn()) {
  console.log('用户已登录');
}

// 获取当前用户信息
const user = getCurrentUser();

// 要求用户登录
if (!requireLogin()) {
  return; // 用户未登录，已显示登录提示
}
```

## 使用方法

### 1. 在页面中检查登录状态

```javascript
// 方法1：使用全局app实例
const app = getApp();

Page({
  onShow() {
    // 检查登录状态
    app.checkLoginStatus();
    
    // 检查是否已登录
    if (!app.globalData.isLoggedIn) {
      // 处理未登录情况
    }
  }
});

// 方法2：使用工具函数
import { requireLogin, isLoggedIn } from '~/utils/auth';

Page({
  onSomeAction() {
    if (!requireLogin()) {
      return; // 用户未登录，已显示提示
    }
    
    // 执行需要登录的操作
  }
});
```

### 2. 在登录页面设置登录状态

```javascript
const app = getApp();

// 登录成功后
if (result.code === 0) {
  // 使用全局方法设置登录状态
  app.setLoginStatus(result.data.userInfo, result.data.token);
  
  // 跳转到首页
  wx.switchTab({
    url: '/pages/home/<USER>',
  });
}
```

### 3. 退出登录

```javascript
const app = getApp();

// 方法1：使用全局方法
app.logout();

// 方法2：使用工具函数
import { logout } from '~/utils/auth';
logout();
```

### 4. 角色检查

```javascript
import { isTeacher, isStudent, hasRole } from '~/utils/auth';

// 检查是否是教师
if (isTeacher()) {
  console.log('当前用户是教师');
}

// 检查是否是学生
if (isStudent()) {
  console.log('当前用户是学生');
}

// 检查特定角色
if (hasRole(2)) {
  console.log('当前用户是教师');
}
```

## 最佳实践

### 1. 页面生命周期中的登录检查

```javascript
const app = getApp();

Page({
  onShow() {
    // 每次页面显示时检查登录状态
    app.checkLoginStatus();
  },
  
  onLoad() {
    // 页面加载时检查是否需要登录
    if (!app.requireLogin()) {
      wx.navigateBack();
      return;
    }
  }
});
```

### 2. 需要登录的操作

```javascript
import { requireLogin } from '~/utils/auth';

Page({
  onUpload() {
    // 检查登录状态
    if (!requireLogin('请先登录后再上传作品')) {
      return;
    }
    
    // 执行上传操作
    this.doUpload();
  }
});
```

### 3. 获取用户信息

```javascript
import { getCurrentUser } from '~/utils/auth';

Page({
  onLoad() {
    const user = getCurrentUser();
    if (user) {
      this.setData({
        userName: user.nickname,
        userRole: user.role
      });
    }
  }
});
```

## 注意事项

1. **页面切换时的状态检查**：建议在每个页面的 `onShow` 生命周期中调用 `app.checkLoginStatus()`

2. **token过期处理**：系统会自动处理token过期，无需手动处理

3. **兜底机制**：工具函数提供了兜底机制，即使app实例不可用也能正常工作

4. **事件总线**：登录状态变化会触发 `login-status-change` 事件，可以监听此事件来响应登录状态变化

## 事件监听

```javascript
const app = getApp();

// 监听登录状态变化
app.eventBus.on('login-status-change', (isLoggedIn) => {
  console.log('登录状态变化:', isLoggedIn);
  // 处理登录状态变化
});
```

这个系统确保了用户登录状态在整个应用中的一致性和持久性，解决了页面切换时登录状态丢失的问题。
