package com.meiyuan.artevaluation.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评价实体类
 */
@Data
@TableName("evaluations")
public class Evaluation {

    /**
     * 评价ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 作品ID
     */
    private Long artworkId;

    /**
     * 类型：1-AI评价，2-教师评价
     */
    private Integer type;

    /**
     * 评价者ID（教师评价时使用）
     */
    private Long evaluatorId;

    /**
     * 技术分数
     */
    private BigDecimal technicalScore;

    /**
     * 技术分析
     */
    private String technicalAnalysis;

    /**
     * 问题诊断
     */
    private String problemDiagnosis;

    /**
     * 改进方案
     */
    private String improvementPlan;

    /**
     * 阶段目标
     */
    private String stageGoals;

    /**
     * 参考教材
     */
    private String referenceMaterials;

    /**
     * 总分
     */
    private BigDecimal overallScore;

    /**
     * 评分详情（JSON格式）
     */
    private String scores;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    private Integer deleted;

    // 以下字段不存储在数据库中，用于返回给前端
    /**
     * 作品标题（非数据库字段）
     */
    @TableField(exist = false)
    private String artworkTitle;

    /**
     * 作品封面（非数据库字段）
     */
    @TableField(exist = false)
    private String artworkCover;
} 