package com.meiyuan.artevaluation.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 作品实体类
 */
@Data
@TableName("artworks")
public class Artwork {

    /**
     * 作品ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 作品标题
     */
    private String title;

    /**
     * 图片URL数组，JSON格式
     */
    private String images;

    /**
     * 状态：1-待评价，2-已评价
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    private Integer deleted;
} 