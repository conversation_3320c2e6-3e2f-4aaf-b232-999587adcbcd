package com.meiyuan.artevaluation.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpSession;

/**
 * 管理员登录控制器
 */
@Slf4j
@Controller
@RequestMapping("/admin")
public class AdminLoginController {

    // 写死的管理员账号
    private static final String ADMIN_USERNAME = "admin";
    private static final String ADMIN_PASSWORD = "admin123";
    private static final String SESSION_ADMIN_KEY = "admin_logged_in";

    /**
     * 显示登录页面
     */
    @GetMapping("/login")
    public String loginPage(HttpSession session, Model model) {
        // 如果已经登录，直接跳转到管理首页
        if (isLoggedIn(session)) {
            return "redirect:/admin";
        }
        return "admin/login";
    }

    /**
     * 处理登录请求
     */
    @PostMapping("/login")
    public String login(@RequestParam String username,
                       @RequestParam String password,
                       HttpSession session,
                       Model model) {
        try {
            // 验证用户名和密码
            if (ADMIN_USERNAME.equals(username) && ADMIN_PASSWORD.equals(password)) {
                // 登录成功，设置session
                session.setAttribute(SESSION_ADMIN_KEY, true);
                session.setAttribute("admin_username", username);
                
                log.info("管理员登录成功: {}", username);
                return "redirect:/admin";
            } else {
                // 登录失败
                model.addAttribute("error", "用户名或密码错误");
                log.warn("管理员登录失败: 用户名={}, 密码错误", username);
                return "admin/login";
            }
        } catch (Exception e) {
            log.error("管理员登录异常", e);
            model.addAttribute("error", "登录失败，请重试");
            return "admin/login";
        }
    }

    /**
     * 退出登录
     */
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        String username = (String) session.getAttribute("admin_username");
        session.invalidate();
        log.info("管理员退出登录: {}", username);
        return "redirect:/admin/login";
    }

    /**
     * 检查是否已登录
     */
    public static boolean isLoggedIn(HttpSession session) {
        Boolean loggedIn = (Boolean) session.getAttribute(SESSION_ADMIN_KEY);
        return loggedIn != null && loggedIn;
    }
}
