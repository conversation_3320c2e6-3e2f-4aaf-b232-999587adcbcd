version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: art-evaluation-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-123456}
      MYSQL_DATABASE: ${DB_NAME:-art_evaluation}
      MYSQL_USER: ${DB_USERNAME:-artuser}
      MYSQL_PASSWORD: ${DB_PASSWORD:-123456}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - art-evaluation-network

  # Spring Boot应用
  app:
    build: .
    container_name: art-evaluation-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8080}:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${DB_NAME:-art_evaluation}
      - DB_USERNAME=${DB_USERNAME:-artuser}
      - DB_PASSWORD=${DB_PASSWORD:-123456}
      - FILE_UPLOAD_PATH=/app/uploads
      - FILE_URL_PREFIX=${FILE_URL_PREFIX:-http://localhost:8080/files}
      - ARK_API_KEY=${ARK_API_KEY:-905fab5e-33fa-4641-90b9-ed0bd6c0b6bc}
      - ARK_BASE_URL=${ARK_BASE_URL:-https://ark.cn-beijing.volces.com/api/v3}
      - ARK_MODEL=${ARK_MODEL:-deepseek-r1-250120}
      - LOG_FILE=/app/logs/art-evaluation.log
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    depends_on:
      - mysql
    networks:
      - art-evaluation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
  app_uploads:
  app_logs:

networks:
  art-evaluation-network:
    driver: bridge
