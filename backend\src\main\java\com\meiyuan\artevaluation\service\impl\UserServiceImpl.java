package com.meiyuan.artevaluation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.User;
import com.meiyuan.artevaluation.mapper.UserMapper;
import com.meiyuan.artevaluation.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;

    @Override
    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public User getUserByOpenid(String openid) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getOpenid, openid);
        return userMapper.selectOne(queryWrapper);
    }

    @Override
    public User createOrUpdateUser(User user) {
        User existUser = getUserByOpenid(user.getOpenid());
        if (existUser == null) {
            // 新用户，设置默认值
            if (user.getRole() == null) {
                user.setRole(1);
            }
            if (user.getStatus() == null) {
                user.setStatus(1); // 默认正常状态
            }
            // 如果没有头像，设置默认头像
            if (user.getAvatar() == null || user.getAvatar().trim().isEmpty()) {
                user.setAvatar("/static/default-avatar.svg");
            }
            userMapper.insert(user);
            log.info("创建新用户: openid={}, userId={}, nickname={}", user.getOpenid(), user.getId(), user.getNickname());
            return user;
        } else {
            // 用户已存在，直接返回现有用户信息，避免不必要的更新
            log.info("用户已存在，直接返回: openid={}, userId={}, nickname={}", existUser.getOpenid(), existUser.getId(), existUser.getNickname());
            return existUser;
        }
    }

    @Override
    public Integer getUserRole(Long userId) {
        User user = userMapper.selectById(userId);
        return user != null ? user.getRole() : null;
    }

    @Override
    public long count() {
        return userMapper.selectCount(null);
    }

    @Override
    public IPage<User> getAllUsers(Page<User> page) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(User::getCreatedAt);
        return userMapper.selectPage(page, queryWrapper);
    }

    @Override
    public boolean deleteUser(Long userId) {
        return userMapper.deleteById(userId) > 0;
    }

    @Override
    public boolean updateUser(User user) {
        return userMapper.updateById(user) > 0;
    }
}