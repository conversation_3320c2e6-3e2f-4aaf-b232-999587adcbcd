package com.meiyuan.artevaluation.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评分详情实体类
 */
@Data
@TableName("evaluation_scores")
public class EvaluationScore {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 评价ID
     */
    private Long evaluationId;

    /**
     * 透视准确性分数
     */
    private BigDecimal perspectiveScore;

    /**
     * 比例结构分数
     */
    private BigDecimal proportionScore;

    /**
     * 明暗关系分数
     */
    private BigDecimal lightShadowScore;

    /**
     * 线条质量分数
     */
    private BigDecimal lineQualityScore;

    /**
     * 整体效果分数
     */
    private BigDecimal overallEffectScore;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
} 