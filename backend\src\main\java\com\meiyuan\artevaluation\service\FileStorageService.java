package com.meiyuan.artevaluation.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 文件存储服务
 */
@Slf4j
@Service
public class FileStorageService {
    
    @Value("${file.upload.path}")
    private String uploadPath;
    
    @Value("${file.upload.url-prefix}")
    private String urlPrefix;
    
    @Value("${file.upload.allowed-types}")
    private String allowedTypes;
    
    /**
     * 上传文件
     *
     * @param file 文件
     * @return 访问URL
     */
    public String uploadFile(MultipartFile file) {
        log.info("开始上传文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

        // 验证文件是否为空
        if (file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        // 验证文件类型
        String fileType = getFileExtension(file.getOriginalFilename());
        List<String> allowedTypeList = Arrays.asList(allowedTypes.split(","));

        if (!allowedTypeList.contains(fileType.toLowerCase())) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileType);
        }

        // 生成文件名
        String fileName = generateFileName(file.getOriginalFilename());
        String filePath = uploadPath + fileName;

        log.info("上传路径: {}, 文件名: {}", uploadPath, fileName);

        try {
            // 确保上传目录存在
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                log.info("创建上传目录: {}, 结果: {}", uploadPath, created);
            }

            // 保存文件
            File targetFile = new File(filePath);
            log.info("目标文件路径: {}", targetFile.getAbsolutePath());

            file.transferTo(targetFile);

            log.info("文件上传成功: {}", targetFile.getAbsolutePath());

            // 返回访问URL
            String accessUrl = urlPrefix + fileName;
            log.info("文件访问URL: {}", accessUrl);
            return accessUrl;
        } catch (IOException e) {
            log.error("文件上传失败，路径: {}, 错误: {}", filePath, e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成文件名
     *
     * @param originalName 原始文件名
     * @return 新文件名
     */
    private String generateFileName(String originalName) {
        String extension = getFileExtension(originalName);
        return System.currentTimeMillis() + "_" + IdUtil.fastSimpleUUID() + "." + extension;
    }
    
    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名
     */
    private String getFileExtension(String fileName) {
        if (StrUtil.isBlank(fileName) || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
} 