package com.meiyuan.artevaluation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.TeacherApplication;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 教师认证申请Mapper接口
 */
@Mapper
public interface TeacherApplicationMapper extends BaseMapper<TeacherApplication> {

    /**
     * 根据用户ID查询最新的申请记录
     *
     * @param userId 用户ID
     * @return 最新的申请记录
     */
    TeacherApplication getLatestByUserId(@Param("userId") Long userId);

    /**
     * 分页查询申请列表
     *
     * @param page 分页参数
     * @param status 申请状态（可选）
     * @return 申请列表
     */
    IPage<TeacherApplication> getApplicationsPage(Page<TeacherApplication> page, @Param("status") Integer status);

    /**
     * 统计各状态的申请数量
     *
     * @return 统计结果
     */
    java.util.Map<String, Object> getStatusStatistics();
}
