server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    max-threads: 200
    min-spare-threads: 10

spring:
  profiles:
    active: prod
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:art_evaluation}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:0110}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  thymeleaf:
    cache: true
    mode: HTML
    encoding: UTF-8

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 文件存储配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/www/app/uploads/}
    url-prefix: ${FILE_URL_PREFIX:http://*************:8080/files/}

# 火山方舟AI配置
volcengine:
  ark:
    api-key: ${ARK_API_KEY:905fab5e-33fa-4641-90b9-ed0bd6c0b6bc}
    base-url: ${ARK_BASE_URL:https://ark.cn-beijing.volces.com/api/v3}
    model: ${ARK_MODEL:deepseek-r1-250120}

# 日志配置
logging:
  level:
    root: INFO
    com.meiyuan.artevaluation: INFO
    org.springframework.web: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:/www/app/logs/art-evaluation.log}
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
