@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 美院艺术作品评价系统部署脚本 (Windows版本)
:: 使用方法: deploy.bat [操作]
:: 操作: build|start|stop|restart|logs|clean (默认: restart)

set "ACTION=%~1"
if "%ACTION%"=="" set "ACTION=restart"
set "PROJECT_NAME=art-evaluation"

echo [INFO] 美院艺术作品评价系统部署脚本 (Windows)
echo [INFO] 操作: %ACTION%

:: 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

:: 检查环境文件
if not exist ".env" (
    if exist ".env.example" (
        echo [WARN] 未找到.env文件，正在复制.env.example
        copy ".env.example" ".env"
        echo [WARN] 请编辑.env文件，配置正确的环境变量
    ) else (
        echo [ERROR] 未找到.env文件，请创建环境配置文件
        pause
        exit /b 1
    )
)

:: 根据操作执行相应命令
if "%ACTION%"=="build" goto :build
if "%ACTION%"=="start" goto :start
if "%ACTION%"=="stop" goto :stop
if "%ACTION%"=="restart" goto :restart
if "%ACTION%"=="logs" goto :logs
if "%ACTION%"=="clean" goto :clean
if "%ACTION%"=="help" goto :help

echo [ERROR] 未知操作: %ACTION%
goto :help

:build
echo [STEP] 开始构建应用...
echo [INFO] 正在进行Maven打包...
call mvn clean package -DskipTests
if errorlevel 1 (
    echo [ERROR] Maven打包失败
    pause
    exit /b 1
)

echo [INFO] 正在构建Docker镜像...
docker-compose build
if errorlevel 1 (
    echo [ERROR] Docker镜像构建失败
    pause
    exit /b 1
)
echo [INFO] 应用构建完成
goto :end

:start
echo [STEP] 启动服务...
docker-compose up -d
echo [INFO] 等待服务启动...
timeout /t 10 /nobreak >nul
goto :check_status

:stop
echo [STEP] 停止服务...
docker-compose down
echo [INFO] 服务已停止
goto :end

:restart
echo [STEP] 重启服务...
call :build
call :stop
call :start
goto :end

:logs
echo [STEP] 显示应用日志...
docker-compose logs -f app
goto :end

:clean
echo [STEP] 清理Docker资源...
docker-compose down -v
docker system prune -f
echo [INFO] 清理完成
goto :end

:check_status
echo [STEP] 检查服务状态...
docker-compose ps
echo [INFO] 检查应用健康状态...
timeout /t 5 /nobreak >nul
curl -f http://localhost:8080/actuator/health >nul 2>&1
if errorlevel 1 (
    echo [WARN] 应用健康检查失败，请查看日志
) else (
    echo [INFO] 应用健康检查通过
)
goto :end

:help
echo 美院艺术作品评价系统部署脚本 (Windows)
echo.
echo 使用方法:
echo   deploy.bat [操作]
echo.
echo 操作:
echo   build   - 构建应用
echo   start   - 启动服务
echo   stop    - 停止服务
echo   restart - 重启服务 (默认)
echo   logs    - 查看日志
echo   clean   - 清理资源
echo   help    - 显示帮助
echo.
echo 示例:
echo   deploy.bat restart  # 重启服务
echo   deploy.bat logs     # 查看日志
goto :end

:end
if "%ACTION%"=="logs" goto :eof
echo.
echo [INFO] 操作完成
pause
