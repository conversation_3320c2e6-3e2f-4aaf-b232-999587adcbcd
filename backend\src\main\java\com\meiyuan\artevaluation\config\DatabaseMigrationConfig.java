package com.meiyuan.artevaluation.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 数据库迁移配置
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseMigrationConfig implements CommandLineRunner {

    private final JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        try {
            // 检查scores字段是否存在
            String checkColumnSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS " +
                    "WHERE TABLE_SCHEMA = 'art_evaluation' " +
                    "AND TABLE_NAME = 'evaluations' " +
                    "AND COLUMN_NAME = 'scores'";
            
            Integer columnExists = jdbcTemplate.queryForObject(checkColumnSql, Integer.class);
            
            if (columnExists == null || columnExists == 0) {
                log.info("添加evaluations表的scores字段...");
                String addColumnSql = "ALTER TABLE evaluations ADD COLUMN scores json DEFAULT NULL COMMENT '评分详情（JSON格式）' AFTER overall_score";
                jdbcTemplate.execute(addColumnSql);
                log.info("scores字段添加成功");
            } else {
                log.info("scores字段已存在，跳过迁移");
            }
        } catch (Exception e) {
            log.error("数据库迁移失败", e);
        }
    }
}
