-- 数据库更新脚本
USE art_evaluation;

-- 为users表添加status字段（如果不存在）
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-正常' AFTER `role`;

-- 更新现有用户的status字段为1（正常状态）
UPDATE `users` SET `status` = 1 WHERE `status` IS NULL OR `status` = 0;

-- 为evaluations表添加scores字段（如果不存在）
ALTER TABLE `evaluations` ADD COLUMN IF NOT EXISTS `scores` json DEFAULT NULL COMMENT '评分详情（JSON格式）' AFTER `overall_score`;
