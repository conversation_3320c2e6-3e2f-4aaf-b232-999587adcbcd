<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师认证管理 - 美院作品评价管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sidebar-width: 250px;
            --header-height: 60px;
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: block;
            padding: 15px 25px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: var(--accent-color);
        }

        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .header {
            background: white;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .page-title {
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .filter-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background-color: #d1edff;
            color: #0c5460;
        }
        
        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .pagination-container {
            padding: 20px;
            display: flex;
            justify-content: center;
        }
        
        .btn-action {
            margin-right: 5px;
            padding: 4px 8px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>管理系统</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="/admin"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/users"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/artworks"><i class="fas fa-palette"></i> 作品管理</a></li>
            <li><a href="/admin/evaluations"><i class="fas fa-star"></i> 评价管理</a></li>
            <li><a href="/admin/teacher-applications" class="active"><i class="fas fa-user-graduate"></i> 教师认证</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
        </ul>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 头部 -->
        <div class="header">
            <h5 class="mb-0">教师认证管理</h5>
            <div class="d-flex align-items-center">
                <span class="text-muted me-3">美院作品评价管理系统</span>
                <a href="/admin/logout" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <h2 class="page-title">教师认证申请管理</h2>

            <!-- 统计卡片 -->
            <div class="stats-cards" th:if="${statistics}">
                <div class="stat-card">
                    <div class="stat-number text-primary" th:text="${statistics.total ?: 0}">0</div>
                    <div class="stat-label">总申请数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-warning" th:text="${statistics.pending ?: 0}">0</div>
                    <div class="stat-label">待审核</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-success" th:text="${statistics.approved ?: 0}">0</div>
                    <div class="stat-label">已通过</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-danger" th:text="${statistics.rejected ?: 0}">0</div>
                    <div class="stat-label">已拒绝</div>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section">
                <form method="get" action="/admin/teacher-applications">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label for="status" class="form-label">申请状态</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">全部状态</option>
                                <option value="0" th:selected="${status == 0}">待审核</option>
                                <option value="1" th:selected="${status == 1}">已通过</option>
                                <option value="2" th:selected="${status == 2}">已拒绝</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 筛选
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 申请列表 -->
            <div class="table-container">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>申请ID</th>
                            <th>申请人</th>
                            <th>真实姓名</th>
                            <th>工作单位</th>
                            <th>职位</th>
                            <th>联系电话</th>
                            <th>申请状态</th>
                            <th>申请时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="teacherApp : ${teacherApps}">
                            <td th:text="${teacherApp.id}">1</td>
                            <td th:text="${teacherApp.userId}">用户ID</td>
                            <td th:text="${teacherApp.realName}">张三</td>
                            <td th:text="${teacherApp.workUnit}">某某大学</td>
                            <td th:text="${teacherApp.position}">教授</td>
                            <td th:text="${teacherApp.phone}">13800138000</td>
                            <td>
                                <span th:if="${teacherApp.status == 0}" class="status-badge status-pending">待审核</span>
                                <span th:if="${teacherApp.status == 1}" class="status-badge status-approved">已通过</span>
                                <span th:if="${teacherApp.status == 2}" class="status-badge status-rejected">已拒绝</span>
                            </td>
                            <td th:text="${#temporals.format(teacherApp.createdAt, 'yyyy-MM-dd HH:mm')}">2023-12-01 10:00</td>
                            <td>
                                <a th:href="@{/admin/teacher-applications/{id}(id=${teacherApp.id})}"
                                   class="btn btn-sm btn-outline-primary btn-action">
                                    <i class="fas fa-eye"></i> 查看
                                </a>
                                <button th:if="${teacherApp.status == 0}"
                                        class="btn btn-sm btn-success btn-action"
                                        th:onclick="'reviewApplication(' + ${teacherApp.id} + ', 1)'">
                                    <i class="fas fa-check"></i> 通过
                                </button>
                                <button th:if="${teacherApp.status == 0}"
                                        class="btn btn-sm btn-danger btn-action"
                                        th:onclick="'reviewApplication(' + ${teacherApp.id} + ', 2)'">
                                    <i class="fas fa-times"></i> 拒绝
                                </button>
                            </td>
                        </tr>
                        <tr th:if="${#lists.isEmpty(teacherApps)}">
                            <td colspan="9" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                暂无申请记录
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- 分页 -->
                <div class="pagination-container" th:if="${totalPages > 1}">
                    <nav>
                        <ul class="pagination">
                            <li class="page-item" th:classappend="${currentPage == 1} ? 'disabled'">
                                <a class="page-link" th:href="@{/admin/teacher-applications(page=${currentPage - 1}, status=${status})}">上一页</a>
                            </li>
                            <li class="page-item" th:each="i : ${#numbers.sequence(1, totalPages)}" 
                                th:classappend="${i == currentPage} ? 'active'">
                                <a class="page-link" th:href="@{/admin/teacher-applications(page=${i}, status=${status})}" th:text="${i}">1</a>
                            </li>
                            <li class="page-item" th:classappend="${currentPage == totalPages} ? 'disabled'">
                                <a class="page-link" th:href="@{/admin/teacher-applications(page=${currentPage + 1}, status=${status})}">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function reviewApplication(applicationId, status) {
            const statusText = status === 1 ? '通过' : '拒绝';
            const remark = prompt(`请输入${statusText}理由（可选）:`);

            if (remark === null) return; // 用户取消

            if (confirm(`确定要${statusText}该申请吗？`)) {
                fetch(`/admin/teacher-applications/${applicationId}/review`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: status,
                        adminRemark: remark || ''
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        alert(data.msg);
                        location.reload();
                    } else {
                        alert('操作失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                });
            }
        }
    </script>
</body>
</html>
