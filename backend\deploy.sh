#!/bin/bash

# 美院艺术作品评价系统部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev|prod (默认: prod)
# 操作: build|start|stop|restart|logs|clean (默认: restart)

set -e

# 默认参数
ENVIRONMENT=${1:-prod}
ACTION=${2:-restart}
PROJECT_NAME="art-evaluation"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 检查环境文件
check_env_file() {
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            log_warn "未找到.env文件，正在复制.env.example"
            cp .env.example .env
            log_warn "请编辑.env文件，配置正确的环境变量"
        else
            log_error "未找到.env文件，请创建环境配置文件"
            exit 1
        fi
    fi
}

# 构建应用
build_app() {
    log_step "开始构建应用..."
    
    # Maven打包
    log_info "正在进行Maven打包..."
    mvn clean package -DskipTests
    
    if [ ! -f "target/${PROJECT_NAME}-0.0.1-SNAPSHOT.jar" ]; then
        log_error "Maven打包失败，未找到jar文件"
        exit 1
    fi
    
    log_info "Maven打包完成"
    
    # Docker构建
    log_info "正在构建Docker镜像..."
    docker-compose build
    
    log_info "应用构建完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services
}

# 停止服务
stop_services() {
    log_step "停止服务..."
    docker-compose down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_step "重启服务..."
    stop_services
    start_services
}

# 检查服务状态
check_services() {
    log_step "检查服务状态..."
    
    # 检查容器状态
    if docker-compose ps | grep -q "Up"; then
        log_info "容器运行状态:"
        docker-compose ps
        
        # 检查应用健康状态
        log_info "检查应用健康状态..."
        sleep 5
        
        if curl -f http://localhost:8080/actuator/health &> /dev/null; then
            log_info "✅ 应用健康检查通过"
        else
            log_warn "⚠️  应用健康检查失败，请查看日志"
        fi
    else
        log_error "❌ 服务启动失败"
        docker-compose logs
    fi
}

# 查看日志
show_logs() {
    log_step "显示应用日志..."
    docker-compose logs -f app
}

# 清理资源
clean_resources() {
    log_step "清理Docker资源..."
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除镜像
    docker rmi $(docker images "${PROJECT_NAME}*" -q) 2>/dev/null || true
    
    # 清理未使用的资源
    docker system prune -f
    
    log_info "清理完成"
}

# 显示帮助信息
show_help() {
    echo "美院艺术作品评价系统部署脚本"
    echo ""
    echo "使用方法:"
    echo "  ./deploy.sh [环境] [操作]"
    echo ""
    echo "环境:"
    echo "  dev   - 开发环境"
    echo "  prod  - 生产环境 (默认)"
    echo ""
    echo "操作:"
    echo "  build   - 构建应用"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务 (默认)"
    echo "  logs    - 查看日志"
    echo "  status  - 检查状态"
    echo "  clean   - 清理资源"
    echo "  help    - 显示帮助"
    echo ""
    echo "示例:"
    echo "  ./deploy.sh prod restart  # 生产环境重启"
    echo "  ./deploy.sh dev logs      # 开发环境查看日志"
}

# 主函数
main() {
    log_info "美院艺术作品评价系统部署脚本"
    log_info "环境: $ENVIRONMENT, 操作: $ACTION"
    
    # 检查Docker
    check_docker
    
    # 检查环境文件
    check_env_file
    
    case $ACTION in
        "build")
            build_app
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            build_app
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "status")
            check_services
            ;;
        "clean")
            clean_resources
            ;;
        "help")
            show_help
            ;;
        *)
            log_error "未知操作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main
