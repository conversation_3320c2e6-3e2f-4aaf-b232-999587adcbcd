.certification-container {
  padding: 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 状态卡片 */
.status-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.status-title {
  margin-left: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.status-content {
  padding-left: 56rpx;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-item .label {
  font-size: 28rpx;
  color: #8a8a8a;
  margin-right: 16rpx;
}

.status-item .value {
  font-size: 28rpx;
  color: #262626;
  flex: 1;
}

/* 表单容器 */
.form-container {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.form-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
  display: block;
  margin-bottom: 12rpx;
}

.form-desc {
  font-size: 28rpx;
  color: #8a8a8a;
  line-height: 1.5;
}

/* 分组标题 */
.section-header {
  background-color: #f8f8f8 !important;
  font-weight: 600 !important;
  color: #262626 !important;
}

/* 上传区域 */
.upload-section {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.title-required {
  color: #e34d59;
  margin-left: 8rpx;
  font-size: 32rpx;
}

.upload-desc {
  font-size: 28rpx;
  color: #8a8a8a;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover;
  border: 1rpx solid #e7e7e7;
}

.delete-btn {
  position: absolute;
  top: -12rpx;
  right: -12rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: #e34d59;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(227, 77, 89, 0.3);
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.upload-btn:active {
  background-color: #f0f0f0;
  border-color: #bfbfbf;
}

.upload-text {
  font-size: 24rpx;
  color: #c5c5c5;
  margin-top: 12rpx;
}

/* 提交按钮 */
.submit-container {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 无法申请提示 */
.no-apply-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.tip-text {
  font-size: 28rpx;
  color: #8a8a8a;
  margin-top: 24rpx;
}

/* 加载状态 */
.certification-container > .t-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
