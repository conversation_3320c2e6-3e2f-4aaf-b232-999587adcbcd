package com.meiyuan.artevaluation.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 火山方舟配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "volcengine.ark")
public class VolcengineArkConfig {

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * 基础URL
     */
    private String baseUrl;

    /**
     * 模型名称/端点ID
     */
    private String model;

    /**
     * 连接超时时间（秒）
     */
    private Integer connectTimeout = 20;

    /**
     * 读取超时时间（秒）
     */
    private Integer timeout = 120;

    /**
     * 重试次数
     */
    private Integer retryTimes = 2;
}
