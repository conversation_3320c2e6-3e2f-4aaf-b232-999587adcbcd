<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作品详情 - 美院艺术作品评价系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .artwork-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .evaluation-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        .ai-evaluation {
            border-left-color: #28a745;
        }
        .teacher-evaluation {
            border-left-color: #ffc107;
        }
        .score-badge {
            font-size: 1.2em;
            padding: 8px 16px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 返回按钮 -->
            <div class="col-12 mb-3">
                <a href="/admin/artworks" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回作品列表
                </a>
                <button class="btn btn-outline-secondary ms-2" onclick="history.back()">
                    <i class="fas fa-undo"></i> 返回上页
                </button>
            </div>

            <!-- 作品信息 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">作品信息</h5>
                    </div>
                    <div class="card-body">
                        <h4 th:text="${artwork.title}">作品标题</h4>
                        <p class="text-muted mb-2">
                            <i class="fas fa-user"></i> 用户ID: <span th:text="${artwork.userId}">1</span>
                        </p>
                        <p class="text-muted mb-2">
                            <i class="fas fa-calendar"></i> 上传时间: 
                            <span th:text="${#temporals.format(artwork.createdAt, 'yyyy-MM-dd HH:mm:ss')}">2023-12-01 10:00:00</span>
                        </p>
                        <p class="text-muted mb-3">
                            <i class="fas fa-info-circle"></i> 状态: 
                            <span th:if="${artwork.status == 1}" class="badge bg-warning">待评价</span>
                            <span th:if="${artwork.status == 2}" class="badge bg-success">已评价</span>
                        </p>
                        
                        <!-- 作品图片 -->
                        <div class="mb-3">
                            <h6>作品图片</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3" th:each="image : ${images}">
                                    <img th:src="${image}" class="artwork-image" alt="作品图片" 
                                         onclick="showImageModal(this.src)">
                                </div>
                                <div th:if="${#lists.isEmpty(images)}" class="col-12">
                                    <p class="text-muted">暂无图片</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 评价信息 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">评价信息</h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(evaluations)}" class="text-center text-muted py-4">
                            <i class="fas fa-comment-slash fa-3x mb-3"></i>
                            <p>暂无评价</p>
                        </div>

                        <div th:each="evaluation : ${evaluations}"
                             th:class="'card evaluation-card ' + (${evaluation.type == 1} ? 'ai-evaluation' : 'teacher-evaluation')">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>
                                    <i th:if="${evaluation.type == 1}" class="fas fa-robot text-success"></i>
                                    <i th:if="${evaluation.type == 2}" class="fas fa-user-tie text-warning"></i>
                                    <span th:if="${evaluation.type == 1}">AI评价</span>
                                    <span th:if="${evaluation.type == 2}">教师评价</span>
                                </span>
                                <span th:if="${evaluation.overallScore}" 
                                      class="badge score-badge"
                                      th:classappend="${evaluation.overallScore >= 80} ? 'bg-success' : (${evaluation.overallScore >= 60} ? 'bg-warning' : 'bg-danger')"
                                      th:text="${evaluation.overallScore} + '分'">85分</span>
                            </div>
                            <div class="card-body">
                                <div th:if="${evaluation.technicalAnalysis}" class="mb-3">
                                    <h6><i class="fas fa-search"></i> 技术分析</h6>
                                    <p th:text="${evaluation.technicalAnalysis}">技术分析内容</p>
                                </div>
                                
                                <div th:if="${evaluation.problemDiagnosis}" class="mb-3">
                                    <h6><i class="fas fa-exclamation-triangle"></i> 问题诊断</h6>
                                    <p th:text="${evaluation.problemDiagnosis}">问题诊断内容</p>
                                </div>
                                
                                <div th:if="${evaluation.improvementPlan}" class="mb-3">
                                    <h6><i class="fas fa-lightbulb"></i> 改进建议</h6>
                                    <p th:text="${evaluation.improvementPlan}">改进建议内容</p>
                                </div>
                                
                                <div th:if="${evaluation.stageGoals}" class="mb-3">
                                    <h6><i class="fas fa-target"></i> 阶段目标</h6>
                                    <p th:text="${evaluation.stageGoals}">阶段目标内容</p>
                                </div>
                                
                                <div th:if="${evaluation.referenceMaterials}" class="mb-3">
                                    <h6><i class="fas fa-book"></i> 参考资料</h6>
                                    <p th:text="${evaluation.referenceMaterials}">参考资料内容</p>
                                </div>
                                
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i> 
                                    <span th:text="${#temporals.format(evaluation.createdAt, 'yyyy-MM-dd HH:mm:ss')}">2023-12-01 10:00:00</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="图片预览">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showImageModal(imageSrc) {
            document.getElementById('modalImage').src = imageSrc;
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            modal.show();
        }
    </script>
</body>
</html>
