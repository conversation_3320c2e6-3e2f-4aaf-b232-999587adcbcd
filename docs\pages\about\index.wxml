<t-toast id="t-toast" />

<view class="about">
  <!-- 应用信息 -->
  <view class="app-info">
    <view class="app-logo">
      <image src="{{appInfo.logo}}" mode="aspectFit" />
    </view>
    <view class="app-name">{{appInfo.name}}</view>
    <view class="app-version">版本 {{appInfo.version}}</view>
    <view class="app-desc">{{appInfo.description}}</view>
  </view>

  <!-- 功能特色 -->
  <view class="features-section">
    <view class="section-title">功能特色</view>
    <view class="features-grid">
      <view class="feature-item" wx:for="{{features}}" wx:key="title">
        <view class="feature-icon">
          <t-icon name="{{item.icon}}" size="48rpx" color="#1890ff" />
        </view>
        <view class="feature-content">
          <view class="feature-title">{{item.title}}</view>
          <view class="feature-desc">{{item.desc}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 团队介绍 -->
  <view class="team-section">
    <view class="section-title">团队介绍</view>
    <view class="team-list">
      <view 
        class="team-member" 
        wx:for="{{teamMembers}}" 
        wx:key="name"
        data-index="{{index}}"
        bindtap="onMemberTap"
      >
        <view class="member-avatar">
          <t-avatar image="{{item.avatar}}" size="large" />
        </view>
        <view class="member-info">
          <view class="member-name">{{item.name}}</view>
          <view class="member-role">{{item.role}}</view>
        </view>
        <t-icon name="chevron-right" size="32rpx" color="#999" />
      </view>
    </view>
  </view>

  <!-- 联系我们 -->
  <view class="contact-section">
    <view class="section-title">联系我们</view>
    <view class="contact-list">
      <view class="contact-item">
        <view class="contact-icon">
          <t-icon name="mail" size="40rpx" color="#1890ff" />
        </view>
        <view class="contact-content">
          <view class="contact-label">邮箱</view>
          <view class="contact-value">{{contactInfo.email}}</view>
        </view>
        <t-button 
          size="small" 
          variant="outline"
          data-type="email"
          data-value="{{contactInfo.email}}"
          bindtap="onCopyContact"
        >
          复制
        </t-button>
      </view>

      <view class="contact-item">
        <view class="contact-icon">
          <t-icon name="call" size="40rpx" color="#07c160" />
        </view>
        <view class="contact-content">
          <view class="contact-label">电话</view>
          <view class="contact-value">{{contactInfo.phone}}</view>
        </view>
        <t-button 
          size="small" 
          variant="outline"
          data-type="phone"
          data-value="{{contactInfo.phone}}"
          bindtap="onCopyContact"
        >
          复制
        </t-button>
      </view>

      <view class="contact-item">
        <view class="contact-icon">
          <t-icon name="location" size="40rpx" color="#ff6b35" />
        </view>
        <view class="contact-content">
          <view class="contact-label">地址</view>
          <view class="contact-value">{{contactInfo.address}}</view>
        </view>
        <t-button 
          size="small" 
          variant="outline"
          data-type="address"
          data-value="{{contactInfo.address}}"
          bindtap="onCopyContact"
        >
          复制
        </t-button>
      </view>
    </view>
  </view>

  <!-- 其他信息 -->
  <view class="other-section">
    <t-cell-group theme="card">
      <t-cell 
        title="检查更新" 
        arrow 
        hover
        bindtap="onCheckUpdate"
      />
      <t-cell 
        title="用户协议" 
        arrow 
        hover
        bindtap="onUserAgreement"
      />
      <t-cell 
        title="隐私政策" 
        arrow 
        hover
        bindtap="onPrivacyPolicy"
      />
    </t-cell-group>
  </view>

  <!-- 版权信息 -->
  <view class="copyright">
    <text>© 2024 美术学院. All rights reserved.</text>
  </view>
</view>
