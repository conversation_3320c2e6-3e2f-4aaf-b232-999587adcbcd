<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiyuan.artevaluation.mapper.TeacherApplicationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.meiyuan.artevaluation.entity.TeacherApplication">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="id_card" property="idCard" jdbcType="VARCHAR"/>
        <result column="work_unit" property="workUnit" jdbcType="VARCHAR"/>
        <result column="position" property="position" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="certificate_images" property="certificateImages" jdbcType="LONGVARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="admin_remark" property="adminRemark" jdbcType="LONGVARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="reviewed_at" property="reviewedAt" jdbcType="TIMESTAMP"/>
        <result column="reviewed_by" property="reviewedBy" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, real_name, id_card, work_unit, position, phone, email,
        certificate_images, status, admin_remark, created_at, updated_at,
        reviewed_at, reviewed_by
    </sql>

    <!-- 根据用户ID查询最新的申请记录 -->
    <select id="getLatestByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM teacher_applications
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
        LIMIT 1
    </select>

    <!-- 分页查询申请列表 -->
    <select id="getApplicationsPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM teacher_applications
        <where>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 统计各状态的申请数量 -->
    <select id="getStatusStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as rejected
        FROM teacher_applications
    </select>

</mapper>
