package com.meiyuan.artevaluation.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 教师认证申请表迁移配置
 */
@Slf4j
@Component
public class TeacherApplicationTableMigration implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        try {
            // 检查表是否存在
            String checkTableSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'art_evaluation' AND table_name = 'teacher_applications'";
            Integer count = jdbcTemplate.queryForObject(checkTableSql, Integer.class);
            
            if (count == null || count == 0) {
                log.info("创建教师认证申请表...");
                
                String createTableSql = """
                    CREATE TABLE teacher_applications (
                        id bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
                        user_id bigint NOT NULL COMMENT '用户ID',
                        real_name varchar(50) NOT NULL COMMENT '真实姓名',
                        id_card varchar(18) NOT NULL COMMENT '身份证号',
                        work_unit varchar(200) NOT NULL COMMENT '工作单位',
                        position varchar(100) NOT NULL COMMENT '职位',
                        phone varchar(20) NOT NULL COMMENT '联系电话',
                        email varchar(100) DEFAULT NULL COMMENT '邮箱',
                        certificate_images text COMMENT '认证材料图片（JSON数组）',
                        status tinyint NOT NULL DEFAULT '0' COMMENT '申请状态：0-待审核，1-审核通过，2-审核拒绝',
                        admin_remark text COMMENT '管理员备注',
                        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
                        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                        reviewed_at datetime DEFAULT NULL COMMENT '审核时间',
                        reviewed_by bigint DEFAULT NULL COMMENT '审核人ID',
                        PRIMARY KEY (id),
                        KEY idx_user_id (user_id),
                        KEY idx_status (status),
                        KEY idx_created_at (created_at)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师认证申请表'
                    """;
                
                jdbcTemplate.execute(createTableSql);
                log.info("教师认证申请表创建成功");
            } else {
                log.info("教师认证申请表已存在，跳过创建");
            }
        } catch (Exception e) {
            log.error("创建教师认证申请表失败", e);
        }
    }
}
