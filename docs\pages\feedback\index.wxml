<t-toast id="t-toast" />

<view class="feedback">
  <view class="feedback-form">
    <!-- 反馈类型 -->
    <view class="form-section">
      <view class="section-title">反馈类型 <text class="required">*</text></view>
      <t-radio-group value="{{feedbackType}}" bind:change="onTypeChange">
        <view class="radio-list">
          <t-radio 
            wx:for="{{typeOptions}}" 
            wx:key="value"
            value="{{item.value}}" 
            label="{{item.label}}"
            class="radio-item"
          />
        </view>
      </t-radio-group>
    </view>

    <!-- 反馈内容 -->
    <view class="form-section">
      <view class="section-title">
        反馈内容 <text class="required">*</text>
        <text class="char-count">({{feedbackContent.length}}/500)</text>
      </view>
      <textarea
        class="feedback-textarea"
        value="{{feedbackContent}}"
        placeholder="请详细描述您遇到的问题或建议，我们会认真对待每一条反馈"
        maxlength="500"
        auto-height
        bindinput="onContentInput"
      />
    </view>

    <!-- 图片上传 -->
    <view class="form-section">
      <view class="section-title">
        相关图片 
        <text class="optional">(可选，最多{{maxImages}}张)</text>
      </view>
      <view class="image-upload">
        <view class="image-list">
          <view 
            class="image-item" 
            wx:for="{{images}}" 
            wx:key="index"
            data-index="{{index}}"
            bindtap="onPreviewImage"
          >
            <image src="{{item.url}}" mode="aspectFill" />
            <view 
              class="delete-btn"
              data-index="{{index}}"
              catchtap="onDeleteImage"
            >
              <t-icon name="close" size="24rpx" color="white" />
            </view>
          </view>
          
          <view 
            class="upload-btn" 
            wx:if="{{images.length < maxImages}}"
            bindtap="onChooseImage"
          >
            <t-icon name="add" size="48rpx" color="#999" />
            <text>添加图片</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="form-section">
      <view class="section-title">
        联系方式 
        <text class="optional">(可选)</text>
      </view>
      <input
        class="feedback-input"
        value="{{contactInfo}}"
        placeholder="请留下您的联系方式，方便我们与您沟通"
        bindinput="onContactInput"
      />
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <t-button 
        theme="primary" 
        size="large"
        loading="{{submitting}}"
        disabled="{{submitting}}"
        bindtap="onSubmit"
      >
        {{submitting ? '提交中...' : '提交反馈'}}
      </t-button>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-section">
      <view class="tips-title">
        <t-icon name="info-circle" size="32rpx" color="#1890ff" />
        <text>温馨提示</text>
      </view>
      <view class="tips-content">
        <text>• 我们会在1-3个工作日内处理您的反馈</text>
        <text>• 如需紧急处理，请拨打客服电话：400-123-4567</text>
        <text>• 您的个人信息将被严格保护</text>
      </view>
    </view>
  </view>
</view>
