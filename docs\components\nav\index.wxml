<view class="home-navbar">
  <t-navbar title="{{ navType === 'search' ? '' : titleText }}">
    <view slot="left">
      <view class="home-navbar__left">
        <t-icon
          class="home-navbar__icon"
          bind:tap="openAdvancedSearch"
          name="filter"
          size="48rpx"
          wx:if="{{navType === 'search' && showAdvancedFilter}}"
        />
        <t-search
          shape="round"
          placeholder="{{searchPlaceholder}}"
          value="{{searchValue}}"
          bind:change="onSearchChange"
          bind:submit="onSearchSubmit"
          bind:clear="onSearchClear"
          wx:if="{{navType === 'search'}}"
        />
      </view>
    </view>
  </t-navbar>

  <!-- 高级搜索下拉面板 - 只在推荐tab且显示高级筛选时显示 -->
  <view class="advanced-search-panel" wx:if="{{showAdvancedSearch && showAdvancedFilter}}">
    <view class="advanced-search-content">
      <view class="search-filter-item">
        <text class="filter-label">作品类型</text>
        <t-dropdown-menu>
          <t-dropdown-item
            value="{{selectedType}}"
            options="{{typeOptions}}"
            bind:change="onTypeChange"
          />
        </t-dropdown-menu>
      </view>

      <view class="search-filter-item">
        <text class="filter-label">评价状态</text>
        <t-dropdown-menu>
          <t-dropdown-item
            value="{{selectedStatus}}"
            options="{{statusOptions}}"
            bind:change="onStatusChange"
          />
        </t-dropdown-menu>
      </view>

      <view class="search-filter-item">
        <text class="filter-label">上传时间</text>
        <t-dropdown-menu>
          <t-dropdown-item
            value="{{selectedTime}}"
            options="{{timeOptions}}"
            bind:change="onTimeChange"
          />
        </t-dropdown-menu>
      </view>

      <view class="search-actions">
        <t-button size="small" variant="outline" bind:tap="resetFilters">重置</t-button>
        <t-button size="small" theme="primary" bind:tap="applyFilters">应用筛选</t-button>
      </view>
    </view>
  </view>

  <!-- 遮罩层 -->
  <view class="search-mask" wx:if="{{showAdvancedSearch && showAdvancedFilter}}" bind:tap="closeAdvancedSearch"></view>
</view>
