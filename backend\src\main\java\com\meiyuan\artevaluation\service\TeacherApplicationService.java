package com.meiyuan.artevaluation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.TeacherApplication;

import java.util.Map;

/**
 * 教师认证申请服务接口
 */
public interface TeacherApplicationService {

    /**
     * 提交教师认证申请
     *
     * @param application 申请信息
     * @return 申请记录
     */
    TeacherApplication submitApplication(TeacherApplication application);

    /**
     * 根据用户ID查询最新的申请记录
     *
     * @param userId 用户ID
     * @return 最新的申请记录
     */
    TeacherApplication getLatestByUserId(Long userId);

    /**
     * 分页查询申请列表
     *
     * @param page 分页参数
     * @param status 申请状态（可选）
     * @return 申请列表
     */
    IPage<TeacherApplication> getApplicationsPage(Page<TeacherApplication> page, Integer status);

    /**
     * 审核申请
     *
     * @param applicationId 申请ID
     * @param status 审核结果（1-通过，2-拒绝）
     * @param adminRemark 管理员备注
     * @param reviewerId 审核人ID
     * @return 是否成功
     */
    boolean reviewApplication(Long applicationId, Integer status, String adminRemark, Long reviewerId);

    /**
     * 根据ID查询申请记录
     *
     * @param id 申请ID
     * @return 申请记录
     */
    TeacherApplication getById(Long id);

    /**
     * 统计各状态的申请数量
     *
     * @return 统计结果
     */
    Map<String, Object> getStatusStatistics();

    /**
     * 检查用户是否可以申请教师认证
     *
     * @param userId 用户ID
     * @return 是否可以申请
     */
    boolean canApply(Long userId);
}
