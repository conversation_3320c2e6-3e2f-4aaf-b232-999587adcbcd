-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS art_evaluation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE art_evaluation;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    nickname VARCHAR(100) COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    role TINYINT DEFAULT 1 COMMENT '角色：1-学生，2-教师，3-管理员',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 作品表
CREATE TABLE IF NOT EXISTS artworks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '作品标题',
    description TEXT COMMENT '作品描述',
    images JSON COMMENT '作品图片URLs（JSON数组）',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    category VARCHAR(50) DEFAULT 'sketch' COMMENT '作品类别',
    tags VARCHAR(500) COMMENT '标签',
    status TINYINT DEFAULT 1 COMMENT '状态：1-待评价，2-已评价',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='作品表';

-- 评价表
CREATE TABLE IF NOT EXISTS evaluations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    artwork_id BIGINT NOT NULL COMMENT '作品ID',
    evaluator_id BIGINT COMMENT '评价者ID（AI评价时为NULL）',
    evaluator_type TINYINT NOT NULL COMMENT '评价者类型：1-AI，2-教师',
    overall_score DECIMAL(5,2) COMMENT '总分',
    technical_analysis TEXT COMMENT '技术分析',
    problem_diagnosis TEXT COMMENT '问题诊断',
    improvement_plan TEXT COMMENT '改进方案',
    stage_goals TEXT COMMENT '阶段目标',
    reference_materials TEXT COMMENT '参考资料',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_artwork_id (artwork_id),
    INDEX idx_evaluator_id (evaluator_id),
    INDEX idx_evaluator_type (evaluator_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价表';

-- 评分详情表
CREATE TABLE IF NOT EXISTS evaluation_scores (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    evaluation_id BIGINT NOT NULL COMMENT '评价ID',
    perspective_score DECIMAL(5,2) COMMENT '透视准确性分数',
    proportion_score DECIMAL(5,2) COMMENT '比例结构分数',
    light_shadow_score DECIMAL(5,2) COMMENT '明暗关系分数',
    line_quality_score DECIMAL(5,2) COMMENT '线条质量分数',
    overall_effect_score DECIMAL(5,2) COMMENT '整体效果分数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_evaluation_id (evaluation_id),
    FOREIGN KEY (evaluation_id) REFERENCES evaluations(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评分详情表';

-- 插入默认用户数据
INSERT IGNORE INTO users (id, username, password, nickname, avatar, role) VALUES 
(10, 'student', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nDfxIkqNEKy', '美院学生', '/static/avatar1.png', 1);

-- 插入测试作品数据（如果不存在）
INSERT IGNORE INTO artworks (id, title, description, images, user_id, category, status) VALUES 
(1, '素描正方体练习1', '基础几何体素描练习', '["http://localhost:8080/files/test1.jpg"]', 10, 'sketch', 2),
(2, '素描正方体练习2', '透视关系练习', '["http://localhost:8080/files/test2.jpg"]', 10, 'sketch', 2),
(3, '素描正方体练习3', '明暗关系练习', '["http://localhost:8080/files/test3.jpg"]', 10, 'sketch', 2),
(4, '素描正方体练习4', '线条质量练习', '["http://localhost:8080/files/test4.jpg"]', 10, 'sketch', 2);
