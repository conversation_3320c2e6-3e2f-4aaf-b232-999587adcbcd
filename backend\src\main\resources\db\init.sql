-- 创建数据库
CREATE DATABASE IF NOT EXISTS art_evaluation DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE art_evaluation;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信openid',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `role` tinyint NOT NULL DEFAULT '1' COMMENT '角色：1-学生，2-教师',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 作品表
CREATE TABLE IF NOT EXISTS `artworks` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '作品ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(100) DEFAULT NULL COMMENT '作品标题',
  `images` json NOT NULL COMMENT '图片URL数组',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-待评价，2-已评价',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='作品表';

-- 评价表
CREATE TABLE IF NOT EXISTS `evaluations` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `artwork_id` bigint NOT NULL COMMENT '作品ID',
  `type` tinyint NOT NULL COMMENT '类型：1-AI评价，2-教师评价',
  `evaluator_id` bigint DEFAULT NULL COMMENT '评价者ID（教师评价时使用）',
  `technical_score` decimal(5,2) DEFAULT NULL COMMENT '技术分数',
  `technical_analysis` text COMMENT '技术分析',
  `problem_diagnosis` text COMMENT '问题诊断',
  `improvement_plan` text COMMENT '改进方案',
  `stage_goals` text COMMENT '阶段目标',
  `reference_materials` text COMMENT '参考教材',
  `overall_score` decimal(5,2) DEFAULT NULL COMMENT '总分',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_artwork_id` (`artwork_id`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价表';

-- 评分详情表
CREATE TABLE IF NOT EXISTS `evaluation_scores` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `evaluation_id` bigint NOT NULL COMMENT '评价ID',
  `perspective_score` decimal(5,2) DEFAULT NULL COMMENT '透视准确性分数',
  `proportion_score` decimal(5,2) DEFAULT NULL COMMENT '比例结构分数',
  `light_shadow_score` decimal(5,2) DEFAULT NULL COMMENT '明暗关系分数',
  `line_quality_score` decimal(5,2) DEFAULT NULL COMMENT '线条质量分数',
  `overall_effect_score` decimal(5,2) DEFAULT NULL COMMENT '整体效果分数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_evaluation_id` (`evaluation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评分详情表';

-- 教师认证申请表
CREATE TABLE IF NOT EXISTS `teacher_applications` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `id_card` varchar(18) NOT NULL COMMENT '身份证号',
  `work_unit` varchar(200) NOT NULL COMMENT '工作单位',
  `position` varchar(100) NOT NULL COMMENT '职位',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `certificate_images` text COMMENT '认证材料图片（JSON数组）',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '申请状态：0-待审核，1-审核通过，2-审核拒绝',
  `admin_remark` text COMMENT '管理员备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `reviewed_at` datetime DEFAULT NULL COMMENT '审核时间',
  `reviewed_by` bigint DEFAULT NULL COMMENT '审核人ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师认证申请表';

-- 初始化管理员账号
INSERT INTO `users` (`openid`, `nickname`, `role`) VALUES ('admin', '管理员', 2);