package com.meiyuan.artevaluation.controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.Artwork;
import com.meiyuan.artevaluation.entity.Evaluation;
import com.meiyuan.artevaluation.entity.EvaluationScore;
import com.meiyuan.artevaluation.service.ArtworkService;
import com.meiyuan.artevaluation.service.EvaluationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作品控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/artwork")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ArtworkController {

    private final ArtworkService artworkService;
    private final EvaluationService evaluationService;

    /**
     * 删除作品
     *
     * @param id 作品ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Map<String, Object> deleteArtwork(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = artworkService.deleteArtworkById(id);
            if (success) {
                result.put("code", 0);
                result.put("msg", "删除成功");
            } else {
                result.put("code", 1);
                result.put("msg", "删除失败，作品不存在或已删除");
            }
        } catch (Exception e) {
            log.error("删除作品异常", e);
            result.put("code", 1);
            result.put("msg", "删除失败: " + e.getMessage());
        }

        return result;
    }


    /**
     * 获取作品详情
     *
     * @param id 作品ID
     * @return 作品详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getArtworkDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Artwork artwork = artworkService.getArtworkById(id);
            if (artwork == null) {
                result.put("code", 1);
                result.put("msg", "作品不存在");
                return result;
            }
            
            // 获取评价信息
            List<Evaluation> evaluations = evaluationService.getArtworkEvaluations(id, null);
            
            Map<String, Object> data = new HashMap<>();
            data.put("artwork", artwork);
            data.put("evaluations", evaluations);
            
            // 如果有评价，获取第一个AI评价的评分详情
            if (!evaluations.isEmpty()) {
                for (Evaluation evaluation : evaluations) {
                    if (evaluation.getType() == 1) { // AI评价
                        EvaluationScore score = evaluationService.getEvaluationScore(evaluation.getId());
                        data.put("score", score);
                        break;
                    }
                }
            }
            
            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", data);
        } catch (Exception e) {
            log.error("获取作品详情异常", e);
            result.put("code", 1);
            result.put("msg", "获取失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 创建作品
     *
     * @param artworkData 作品数据
     * @return 创建结果
     */
    @PostMapping
    public Map<String, Object> createArtwork(@RequestBody Map<String, Object> artworkData) {
        Map<String, Object> result = new HashMap<>();

        try {
            Long userId = Long.valueOf(artworkData.get("userId").toString());
            String title = artworkData.get("title").toString();
            List<String> images = (List<String>) artworkData.get("images");

            // 创建作品记录
            Artwork artwork = new Artwork();
            artwork.setUserId(userId);
            artwork.setTitle(title);
            artwork.setImages(JSONArray.toJSONString(images));
            artwork.setStatus(1); // 待评价

            Long artworkId = artworkService.createArtwork(artwork);

            // 异步创建AI评价（不阻塞上传响应）
            if (!images.isEmpty()) {
                String firstImageUrl = images.get(0);
                log.info("准备创建AI评价，作品ID: {}, 图片URL: {}", artworkId, firstImageUrl);

                // 使用异步方式调用AI评价，避免阻塞用户上传
                new Thread(() -> {
                    try {
                        log.info("开始异步创建AI评价，作品ID: {}", artworkId);
                        Long evaluationId = evaluationService.createAiEvaluation(artworkId, firstImageUrl);
                        log.info("AI评价创建成功，作品ID: {}, 评价ID: {}", artworkId, evaluationId);
                    } catch (Exception e) {
                        log.error("AI评价创建失败，作品ID: {}, 错误: {}", artworkId, e.getMessage(), e);
                    }
                }).start();
            } else {
                log.warn("没有图片，跳过AI评价创建，作品ID: {}", artworkId);
            }

            result.put("code", 0);
            result.put("msg", "创建成功");
            result.put("data", artworkId);
        } catch (Exception e) {
            log.error("创建作品异常", e);
            result.put("code", 1);
            result.put("msg", "创建失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 获取用户作品列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @param status 评价状态筛选
     * @param time 时间筛选
     * @return 作品列表
     */
    @GetMapping("/user/{userId}")
    public Map<String, Object> getUserArtworks(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String time) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<Artwork> pageParam = new Page<>(page, size);
            IPage<Artwork> artworks;

            // 如果有搜索或筛选条件，使用带筛选的查询方法
            if (keyword != null || status != null || time != null) {
                artworks = artworkService.getUserArtworksWithFilter(userId, pageParam, keyword, status, time);
            } else {
                artworks = artworkService.getUserArtworks(userId, pageParam);
            }

            // 为每个作品添加第一张图片和AI评价状态
            List<Map<String, Object>> artworkList = new ArrayList<>();
            for (Artwork artwork : artworks.getRecords()) {
                Map<String, Object> artworkData = new HashMap<>();
                artworkData.put("id", artwork.getId());
                artworkData.put("title", artwork.getTitle());
                artworkData.put("userId", artwork.getUserId());
                artworkData.put("status", artwork.getStatus());
                artworkData.put("createdAt", artwork.getCreatedAt());

                // 获取第一张图片
                if (artwork.getImages() != null && !artwork.getImages().isEmpty()) {
                    try {
                        List<String> images = JSONArray.parseArray(artwork.getImages(), String.class);
                        if (!images.isEmpty()) {
                            artworkData.put("firstImage", images.get(0));
                        }
                    } catch (Exception e) {
                        log.warn("解析作品图片失败: {}", artwork.getId());
                    }
                }

                // 检查是否有AI评价
                List<Evaluation> evaluations = evaluationService.getArtworkEvaluations(artwork.getId(), 1);
                artworkData.put("hasAiEvaluation", !evaluations.isEmpty());

                artworkList.add(artworkData);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("records", artworkList);
            data.put("total", artworks.getTotal());
            data.put("current", artworks.getCurrent());
            data.put("size", artworks.getSize());
            data.put("pages", artworks.getPages());

            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", data);
        } catch (Exception e) {
            log.error("获取用户作品列表异常", e);
            result.put("code", 1);
            result.put("msg", "获取失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取所有作品列表 (教师端)
     *
     * @param page 页码
     * @param size 每页大小
     * @return 作品列表
     */
    @GetMapping("/all")
    public Map<String, Object> getAllArtworks(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        Map<String, Object> result = new HashMap<>();

        try {
            Page<Artwork> pageParam = new Page<>(page, size);
            IPage<Artwork> artworks = artworkService.getAllArtworks(pageParam);

            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", artworks);
        } catch (Exception e) {
            log.error("获取所有作品列表异常", e);
            result.put("code", 1);
            result.put("msg", "获取失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取首页作品列表（推荐作品）
     *
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @param type 作品类型筛选
     * @param status 评价状态筛选
     * @param time 时间筛选
     * @return 作品列表
     */
    @GetMapping("/home")
    public Map<String, Object> getHomeArtworks(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String time) {
        Map<String, Object> result = new HashMap<>();

        try {
            Page<Artwork> pageParam = new Page<>(page, size);
            IPage<Artwork> artworks;

            // 如果有搜索或筛选条件，使用带筛选的查询方法
            if (keyword != null || type != null || status != null || time != null) {
                artworks = artworkService.getAllArtworksWithFilter(pageParam, keyword, type, status, time);
            } else {
                artworks = artworkService.getAllArtworks(pageParam);
            }

            // 为每个作品添加第一张图片和AI评价状态
            List<Map<String, Object>> artworkList = new ArrayList<>();
            for (Artwork artwork : artworks.getRecords()) {
                Map<String, Object> artworkData = new HashMap<>();
                artworkData.put("id", artwork.getId());
                artworkData.put("title", artwork.getTitle());
                artworkData.put("userId", artwork.getUserId());
                artworkData.put("status", artwork.getStatus());
                artworkData.put("createdAt", artwork.getCreatedAt());

                // 获取第一张图片
                if (artwork.getImages() != null && !artwork.getImages().isEmpty()) {
                    try {
                        List<String> images = JSONArray.parseArray(artwork.getImages(), String.class);
                        if (!images.isEmpty()) {
                            artworkData.put("firstImage", images.get(0));
                        }
                    } catch (Exception e) {
                        log.warn("解析作品图片失败: {}", artwork.getId());
                    }
                }

                // 检查是否有AI评价
                List<Evaluation> evaluations = evaluationService.getArtworkEvaluations(artwork.getId(), 1);
                artworkData.put("hasAiEvaluation", !evaluations.isEmpty());

                artworkList.add(artworkData);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("records", artworkList);
            data.put("total", artworks.getTotal());
            data.put("current", artworks.getCurrent());
            data.put("size", artworks.getSize());
            data.put("pages", artworks.getPages());

            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", data);
        } catch (Exception e) {
            log.error("获取首页作品列表异常", e);
            result.put("code", 1);
            result.put("msg", "获取失败: " + e.getMessage());
        }

        return result;
    }
} 