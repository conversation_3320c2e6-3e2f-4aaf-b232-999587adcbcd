package com.meiyuan.artevaluation.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.TeacherApplication;
import com.meiyuan.artevaluation.service.TeacherApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 教师认证申请控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/teacher-application")
public class TeacherApplicationController {

    @Autowired
    private TeacherApplicationService teacherApplicationService;

    /**
     * 提交教师认证申请
     *
     * @param application 申请信息
     * @return 申请结果
     */
    @PostMapping("/submit")
    public Map<String, Object> submitApplication(@RequestBody TeacherApplication application) {
        Map<String, Object> result = new HashMap<>();

        try {
            if (application.getUserId() == null) {
                throw new RuntimeException("用户ID不能为空");
            }

            // 检查是否可以申请
            if (!teacherApplicationService.canApply(application.getUserId())) {
                throw new RuntimeException("您已经是教师或有待审核的申请，无法重复申请");
            }

            TeacherApplication savedApplication = teacherApplicationService.submitApplication(application);

            result.put("code", 0);
            result.put("msg", "申请提交成功");
            result.put("data", savedApplication);

            log.info("用户 {} 提交教师认证申请成功", application.getUserId());

        } catch (Exception e) {
            log.error("提交教师认证申请失败", e);
            result.put("code", 1);
            result.put("msg", e.getMessage());
        }

        return result;
    }

    /**
     * 查询用户的申请状态
     *
     * @param userId 用户ID
     * @return 申请状态
     */
    @GetMapping("/status/{userId}")
    public Map<String, Object> getApplicationStatus(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            TeacherApplication application = teacherApplicationService.getLatestByUserId(userId);

            Map<String, Object> data = new HashMap<>();
            data.put("canApply", teacherApplicationService.canApply(userId));
            data.put("application", application);

            result.put("code", 0);
            result.put("msg", "查询成功");
            result.put("data", data);

        } catch (Exception e) {
            log.error("查询申请状态失败", e);
            result.put("code", 1);
            result.put("msg", "查询失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 分页查询申请列表（管理员使用）
     *
     * @param page 页码
     * @param size 每页大小
     * @param status 申请状态
     * @return 申请列表
     */
    @GetMapping("/list")
    public Map<String, Object> getApplicationList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Integer status) {
        Map<String, Object> result = new HashMap<>();

        try {
            Page<TeacherApplication> pageParam = new Page<>(page, size);
            var applicationPage = teacherApplicationService.getApplicationsPage(pageParam, status);

            Map<String, Object> data = new HashMap<>();
            data.put("records", applicationPage.getRecords());
            data.put("total", applicationPage.getTotal());
            data.put("current", applicationPage.getCurrent());
            data.put("pages", applicationPage.getPages());
            data.put("size", applicationPage.getSize());

            result.put("code", 0);
            result.put("msg", "查询成功");
            result.put("data", data);

        } catch (Exception e) {
            log.error("查询申请列表失败", e);
            result.put("code", 1);
            result.put("msg", "查询失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 审核申请（管理员使用）
     *
     * @param applicationId 申请ID
     * @param reviewRequest 审核请求
     * @return 审核结果
     */
    @PostMapping("/review/{applicationId}")
    public Map<String, Object> reviewApplication(
            @PathVariable Long applicationId,
            @RequestBody Map<String, Object> reviewRequest) {
        Map<String, Object> result = new HashMap<>();

        try {
            Integer status = (Integer) reviewRequest.get("status");
            String adminRemark = (String) reviewRequest.get("adminRemark");
            Long reviewerId = reviewRequest.get("reviewerId") != null ? 
                Long.valueOf(reviewRequest.get("reviewerId").toString()) : 1L; // 默认管理员ID为1

            if (status == null || (status != 1 && status != 2)) {
                throw new RuntimeException("审核状态无效");
            }

            boolean success = teacherApplicationService.reviewApplication(applicationId, status, adminRemark, reviewerId);

            if (success) {
                result.put("code", 0);
                result.put("msg", status == 1 ? "审核通过" : "审核拒绝");
            } else {
                result.put("code", 1);
                result.put("msg", "审核失败");
            }

            log.info("申请 {} 审核完成，状态: {}, 审核人: {}", applicationId, status, reviewerId);

        } catch (Exception e) {
            log.error("审核申请失败", e);
            result.put("code", 1);
            result.put("msg", e.getMessage());
        }

        return result;
    }

    /**
     * 获取申请详情
     *
     * @param applicationId 申请ID
     * @return 申请详情
     */
    @GetMapping("/{applicationId}")
    public Map<String, Object> getApplicationDetail(@PathVariable Long applicationId) {
        Map<String, Object> result = new HashMap<>();

        try {
            TeacherApplication application = teacherApplicationService.getById(applicationId);

            if (application == null) {
                throw new RuntimeException("申请记录不存在");
            }

            result.put("code", 0);
            result.put("msg", "查询成功");
            result.put("data", application);

        } catch (Exception e) {
            log.error("查询申请详情失败", e);
            result.put("code", 1);
            result.put("msg", e.getMessage());
        }

        return result;
    }

    /**
     * 获取申请统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> statistics = teacherApplicationService.getStatusStatistics();

            result.put("code", 0);
            result.put("msg", "查询成功");
            result.put("data", statistics);

        } catch (Exception e) {
            log.error("查询统计信息失败", e);
            result.put("code", 1);
            result.put("msg", "查询失败: " + e.getMessage());
        }

        return result;
    }
}
