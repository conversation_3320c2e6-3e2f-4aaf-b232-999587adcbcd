# 美院艺术作品评价系统 - 部署指南

## 📋 部署前准备

### 1. 服务器要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB，推荐 50GB+
- **网络**: 公网IP，开放 80、443、8080 端口

### 2. 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- Git
- (可选) Nginx

### 3. 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 🚀 快速部署

### 1. 克隆项目
```bash
git clone <your-repository-url>
cd wx_meiyuan_project/backend
```

### 2. 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑环境配置
vim .env
```

**重要配置项**:
```bash
# 数据库配置
DB_PASSWORD=your_secure_password  # 修改为安全密码

# 应用配置
FILE_URL_PREFIX=http://your-domain.com:8080/files  # 修改为你的域名

# AI服务配置
ARK_API_KEY=your_actual_api_key  # 修改为真实的API密钥
```

### 3. 一键部署
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh prod restart
```

## 📝 详细部署步骤

### 1. 手动构建
```bash
# Maven打包
mvn clean package -DskipTests

# 构建Docker镜像
docker-compose build
```

### 2. 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

### 3. 验证部署
```bash
# 检查应用健康状态
curl http://localhost:8080/actuator/health

# 测试API接口
curl http://localhost:8080/api/artwork/home?page=1&size=5
```

## 🔧 常用命令

### 服务管理
```bash
# 启动服务
./deploy.sh prod start

# 停止服务
./deploy.sh prod stop

# 重启服务
./deploy.sh prod restart

# 查看日志
./deploy.sh prod logs

# 检查状态
./deploy.sh prod status

# 清理资源
./deploy.sh prod clean
```

### Docker命令
```bash
# 查看容器状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 进入容器
docker-compose exec app bash

# 重启特定服务
docker-compose restart app

# 查看资源使用
docker stats
```

## 🌐 Nginx配置（可选）

### 1. 安装Nginx
```bash
sudo apt update
sudo apt install nginx
```

### 2. 配置反向代理
```bash
# 复制配置文件
sudo cp nginx.conf /etc/nginx/sites-available/art-evaluation

# 启用站点
sudo ln -s /etc/nginx/sites-available/art-evaluation /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 3. SSL证书（推荐）
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和维护

### 1. 日志管理
```bash
# 应用日志
docker-compose logs app

# 数据库日志
docker-compose logs mysql

# 系统日志
sudo journalctl -u docker

# 清理日志
docker system prune -f
```

### 2. 数据备份
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p art_evaluation > backup_$(date +%Y%m%d).sql

# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz -C /var/lib/docker/volumes/backend_app_uploads/_data .
```

### 3. 性能监控
```bash
# 查看资源使用
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络连接
netstat -tlnp
```

## 🔍 故障排查

### 1. 常见问题

**应用无法启动**
```bash
# 查看详细日志
docker-compose logs app

# 检查端口占用
sudo netstat -tlnp | grep 8080

# 检查磁盘空间
df -h
```

**数据库连接失败**
```bash
# 检查数据库状态
docker-compose logs mysql

# 测试数据库连接
docker-compose exec mysql mysql -u root -p

# 重置数据库密码
docker-compose exec mysql mysql -u root -p -e "ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';"
```

**文件上传失败**
```bash
# 检查上传目录权限
docker-compose exec app ls -la /app/uploads

# 检查磁盘空间
df -h

# 查看应用日志
docker-compose logs app | grep -i upload
```

### 2. 性能优化

**JVM调优**
```bash
# 修改docker-compose.yml中的JAVA_OPTS
JAVA_OPTS: "-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

**数据库优化**
```bash
# 修改MySQL配置
# 在docker-compose.yml中添加配置文件挂载
```

## 📞 技术支持

如果遇到部署问题，请：

1. 查看详细日志: `./deploy.sh prod logs`
2. 检查服务状态: `./deploy.sh prod status`
3. 查看系统资源: `docker stats`
4. 提供错误信息和环境详情

---

**部署成功后，应用将在以下地址可用:**
- API接口: `http://your-domain.com:8080/api`
- 健康检查: `http://your-domain.com:8080/actuator/health`
- 文件服务: `http://your-domain.com:8080/files`
