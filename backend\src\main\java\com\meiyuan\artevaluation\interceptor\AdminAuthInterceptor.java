package com.meiyuan.artevaluation.interceptor;

import com.meiyuan.artevaluation.controller.AdminLoginController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

/**
 * 管理员认证拦截器
 */
@Slf4j
@Component
public class AdminAuthInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        
        // 排除登录页面和登录接口
        if (requestURI.equals("/admin/login") || requestURI.startsWith("/static/") || 
            requestURI.startsWith("/css/") || requestURI.startsWith("/js/") || 
            requestURI.startsWith("/images/")) {
            return true;
        }

        HttpSession session = request.getSession();
        
        // 检查是否已登录
        if (AdminLoginController.isLoggedIn(session)) {
            return true;
        }

        // 未登录，重定向到登录页面
        log.info("未登录用户尝试访问管理页面: {}", requestURI);
        response.sendRedirect("/admin/login");
        return false;
    }
}
