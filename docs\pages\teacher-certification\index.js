import Toast from 'tdesign-miniprogram/toast/index';
import request from '~/api/request';

Page({
  data: {
    loading: false,
    submitting: false,
    
    // 表单数据
    formData: {
      realName: '',
      idCard: '',
      workUnit: '',
      position: '',
      phone: '',
      email: '',
      certificateImages: []
    },
    
    // 表单验证规则
    rules: {
      realName: { required: true, message: '请输入真实姓名' },
      idCard: { required: true, message: '请输入身份证号' },
      workUnit: { required: true, message: '请输入工作单位' },
      position: { required: true, message: '请输入职位' },
      phone: { required: true, message: '请输入联系电话' }
    },
    
    // 申请状态
    applicationStatus: null,
    canApply: true
  },

  onLoad() {
    this.checkApplicationStatus();
  },

  // 检查申请状态
  async checkApplicationStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      const result = await request(`/teacher-application/status/${userInfo.id}`, 'GET');
      
      if (result.code === 0) {
        this.setData({
          canApply: result.data.canApply,
          applicationStatus: result.data.application
        });
      }
    } catch (error) {
      console.error('查询申请状态失败:', error);
      this.showToast('查询申请状态失败', 'error');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 上传认证材料
  async uploadCertificate() {
    try {
      const res = await new Promise((resolve, reject) => {
        wx.chooseImage({
          count: 3 - this.data.formData.certificateImages.length,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera'],
          success: resolve,
          fail: reject
        });
      });

      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        this.setData({ submitting: true });

        const uploadPromises = res.tempFilePaths.map(filePath => {
          return new Promise((resolve, reject) => {
            wx.uploadFile({
              url: wx.getStorageSync('baseUrl') + '/upload/image',
              filePath: filePath,
              name: 'file',
              header: {
                'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
              },
              success: (uploadRes) => {
                try {
                  const data = JSON.parse(uploadRes.data);
                  if (data.code === 0) {
                    resolve(data.data.url);
                  } else {
                    reject(new Error(data.msg || '上传失败'));
                  }
                } catch (e) {
                  reject(new Error('上传响应解析失败'));
                }
              },
              fail: reject
            });
          });
        });

        const uploadedUrls = await Promise.all(uploadPromises);
        const newImages = [...this.data.formData.certificateImages, ...uploadedUrls];
        
        this.setData({
          'formData.certificateImages': newImages
        });

        this.showToast('上传成功', 'success');
      }
    } catch (error) {
      console.error('上传失败:', error);
      this.showToast('上传失败: ' + error.message, 'error');
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 删除图片
  deleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const images = [...this.data.formData.certificateImages];
    images.splice(index, 1);
    
    this.setData({
      'formData.certificateImages': images
    });
  },

  // 预览图片
  previewImage(e) {
    const { url } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: this.data.formData.certificateImages
    });
  },

  // 表单验证
  validateForm() {
    const { formData, rules } = this.data;
    
    for (const field in rules) {
      const rule = rules[field];
      const value = formData[field];
      
      if (rule.required && (!value || value.trim() === '')) {
        this.showToast(rule.message, 'error');
        return false;
      }
    }

    // 身份证号格式验证
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(formData.idCard)) {
      this.showToast('身份证号格式不正确', 'error');
      return false;
    }

    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      this.showToast('手机号格式不正确', 'error');
      return false;
    }

    // 邮箱格式验证（如果填写了）
    if (formData.email && formData.email.trim() !== '') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        this.showToast('邮箱格式不正确', 'error');
        return false;
      }
    }

    if (formData.certificateImages.length === 0) {
      this.showToast('请上传至少一张认证材料', 'error');
      return false;
    }

    return true;
  },

  // 提交申请
  async submitApplication() {
    if (!this.validateForm()) {
      return;
    }

    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      this.showToast('请先登录', 'error');
      return;
    }

    this.setData({ submitting: true });

    try {
      const submitData = {
        ...this.data.formData,
        userId: userInfo.id,
        certificateImages: JSON.stringify(this.data.formData.certificateImages)
      };

      const result = await request('/teacher-application/submit', 'POST', submitData);

      if (result.code === 0) {
        this.showToast('申请提交成功', 'success');
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result.msg || '提交失败');
      }
    } catch (error) {
      console.error('提交申请失败:', error);
      this.showToast(error.message || '提交失败，请重试', 'error');
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 显示提示
  showToast(message, theme = 'info') {
    Toast({
      context: this,
      selector: '#t-toast',
      message,
      theme,
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '教师认证申请',
      path: '/pages/teacher-certification/index'
    };
  }
});
