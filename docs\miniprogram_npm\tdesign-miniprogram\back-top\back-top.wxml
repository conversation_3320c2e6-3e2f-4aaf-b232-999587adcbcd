<import src="../common/template/icon.wxml" />
<wxs src="../common/utils.wxs" module="_" />

<view
  style="{{_._style([style, customStyle])}}"
  class="class {{prefix}}-class {{_.cls(classPrefix, [['fixed', fixed], theme])}}"
  bindtap="toTop"
  aria-role="button"
  hidden="{{hidden}}"
>
  <view class="{{classPrefix}}__icon" aria-hidden>
    <slot name="icon" />
    <template wx:if="{{_icon}}" is="icon" data="{{tClass: prefix + '-class-icon', ..._icon }}" />
  </view>
  <view wx:if="{{!!text}}" class="{{classPrefix}}__text--{{theme}} {{prefix}}-class-text">{{text}}</view>
  <slot />
</view>
