# Nginx 部署配置说明

## 📋 配置文件说明

### 1. 主配置文件
- **nginx.conf** - 完整的nginx配置文件，包含美院艺术作品评价系统配置
- **nginx-https.conf** - HTTPS配置示例（可选）

### 2. 配置特点
- ✅ 基于你现有的nginx配置结构
- ✅ 保留原有的phpmyadmin配置
- ✅ 添加美院艺术作品评价系统配置
- ✅ 支持大文件上传（100MB）
- ✅ 优化的缓存策略
- ✅ 安全配置

## 🚀 部署步骤

### 步骤1：备份现有配置
```bash
# 备份现有nginx配置
cp /www/server/nginx/conf/nginx.conf /www/server/nginx/conf/nginx.conf.backup
```

### 步骤2：替换配置文件
```bash
# 复制新的配置文件
cp nginx.conf /www/server/nginx/conf/nginx.conf
```

### 步骤3：修改域名配置
编辑 `/www/server/nginx/conf/nginx.conf`，将以下内容替换为你的实际域名：
```nginx
server_name your-domain.com;  # 替换为你的域名
```

### 步骤4：创建日志目录
```bash
# 确保日志目录存在
mkdir -p /www/wwwlogs
touch /www/wwwlogs/art-evaluation-access.log
touch /www/wwwlogs/art-evaluation-error.log
```

### 步骤5：创建静态文件目录（可选）
```bash
# 如果需要静态文件服务
mkdir -p /www/wwwroot/art-evaluation/static
```

### 步骤6：测试配置
```bash
# 测试nginx配置
nginx -t

# 如果测试通过，重新加载配置
nginx -s reload
```

## 🔧 配置详解

### 1. 美院艺术作品评价系统配置

#### API代理配置
```nginx
location /api/ {
    proxy_pass http://127.0.0.1:8080/api/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 超时设置
    proxy_connect_timeout 30s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # 缓冲设置
    proxy_buffering on;
    proxy_buffer_size 8k;
    proxy_buffers 8 8k;
}
```

#### 后台管理代理
```nginx
location /admin/ {
    proxy_pass http://127.0.0.1:8080/admin/;
    # ... 其他配置
}
```

#### 文件服务配置
```nginx
location /files/ {
    proxy_pass http://127.0.0.1:8080/files/;
    # 文件缓存设置
    expires 7d;
    add_header Cache-Control "public, immutable";
}
```

### 2. 安全配置

#### 文件上传限制
```nginx
client_max_body_size 100m;  # 支持100MB文件上传
```

#### 安全头设置
```nginx
# 禁止访问隐藏文件
location ~ /\. {
    deny all;
}

# 禁止访问敏感文件
location ~ \.(sql|bak|backup|log)$ {
    deny all;
}
```

### 3. 性能优化

#### 静态文件缓存
```nginx
location /static/ {
    alias /www/wwwroot/art-evaluation/static/;
    expires 30d;
    add_header Cache-Control "public, immutable";
    gzip_static on;
}
```

#### 文件缓存
```nginx
location /files/ {
    expires 7d;
    add_header Cache-Control "public, immutable";
}
```

## 🌐 HTTPS配置（可选）

如果你有SSL证书，可以使用 `nginx-https.conf` 中的配置：

### 1. 修改SSL证书路径
```nginx
ssl_certificate /path/to/your/certificate.crt;
ssl_certificate_key /path/to/your/private.key;
```

### 2. 添加HTTPS配置到主配置文件
将 `nginx-https.conf` 中的server块添加到主配置文件中。

## 📊 访问地址

配置完成后，可以通过以下地址访问：

- **后台管理**: `http://your-domain.com/admin/`
- **API接口**: `http://your-domain.com/api/`
- **文件服务**: `http://your-domain.com/files/`
- **健康检查**: `http://your-domain.com/actuator/health`

## 🔍 故障排查

### 1. 检查nginx状态
```bash
systemctl status nginx
```

### 2. 查看错误日志
```bash
tail -f /www/wwwlogs/nginx_error.log
tail -f /www/wwwlogs/art-evaluation-error.log
```

### 3. 检查端口占用
```bash
netstat -tlnp | grep :80
netstat -tlnp | grep :8080
```

### 4. 测试后端服务
```bash
curl http://127.0.0.1:8080/actuator/health
```

## ⚠️ 注意事项

1. **域名配置**: 记得将 `your-domain.com` 替换为你的实际域名
2. **防火墙**: 确保80端口和8080端口已开放
3. **Java服务**: 确保Spring Boot应用在8080端口正常运行
4. **文件权限**: 确保nginx有权限访问日志目录和静态文件目录
5. **备份**: 部署前务必备份现有配置

## 📞 技术支持

如果遇到问题，可以：
1. 检查nginx错误日志
2. 检查应用程序日志
3. 验证端口和服务状态
4. 确认防火墙配置
