package com.meiyuan.artevaluation.controller;

import com.meiyuan.artevaluation.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class TestController {

    private final UserMapper userMapper;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("msg", "服务正常");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    /**
     * 数据库连接测试
     */
    @GetMapping("/db")
    public Map<String, Object> testDatabase() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 尝试查询用户表
            long userCount = userMapper.selectCount(null);
            result.put("code", 0);
            result.put("msg", "数据库连接正常");
            result.put("userCount", userCount);
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            result.put("code", 1);
            result.put("msg", "数据库连接失败: " + e.getMessage());
        }
        return result;
    }
}
