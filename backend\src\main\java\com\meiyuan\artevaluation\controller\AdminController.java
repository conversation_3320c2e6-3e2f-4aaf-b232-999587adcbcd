package com.meiyuan.artevaluation.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.Artwork;
import com.meiyuan.artevaluation.entity.Evaluation;
import com.meiyuan.artevaluation.entity.EvaluationScore;
import com.meiyuan.artevaluation.entity.TeacherApplication;
import com.meiyuan.artevaluation.entity.User;
import com.meiyuan.artevaluation.service.ArtworkService;
import com.meiyuan.artevaluation.service.EvaluationService;
import com.meiyuan.artevaluation.service.TeacherApplicationService;
import com.meiyuan.artevaluation.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.alibaba.fastjson2.JSONArray;

/**
 * 后台管理控制器
 */
@Slf4j
@Controller
@RequestMapping("/admin")
@RequiredArgsConstructor
public class AdminController {

    private final UserService userService;
    private final ArtworkService artworkService;
    private final EvaluationService evaluationService;
    private final TeacherApplicationService teacherApplicationService;

    /**
     * 管理首页
     */
    @GetMapping({"", "/", "/index"})
    public String index(Model model) {
        try {
            // 统计数据
            long totalUsers = userService.count();
            long totalArtworks = artworkService.count();
            long totalEvaluations = evaluationService.count();
            
            model.addAttribute("totalUsers", totalUsers);
            model.addAttribute("totalArtworks", totalArtworks);
            model.addAttribute("totalEvaluations", totalEvaluations);
            
            // 最近的作品
            Page<Artwork> recentArtworks = new Page<>(1, 5);
            IPage<Artwork> artworkPage = artworkService.getAllArtworks(recentArtworks);

            // 为最近作品添加第一张图片URL
            List<Map<String, Object>> recentArtworkList = new ArrayList<>();
            for (Artwork artwork : artworkPage.getRecords()) {
                Map<String, Object> artworkData = new HashMap<>();
                artworkData.put("id", artwork.getId());
                artworkData.put("title", artwork.getTitle());
                artworkData.put("userId", artwork.getUserId());
                artworkData.put("createdAt", artwork.getCreatedAt());

                // 解析第一张图片
                String firstImage = null;
                if (artwork.getImages() != null && !artwork.getImages().isEmpty()) {
                    try {
                        if (artwork.getImages().startsWith("[")) {
                            List<String> images = JSONArray.parseArray(artwork.getImages(), String.class);
                            if (!images.isEmpty()) {
                                firstImage = images.get(0);
                            }
                        } else {
                            firstImage = artwork.getImages();
                        }
                    } catch (Exception e) {
                        log.warn("解析作品图片失败: {}", artwork.getId());
                    }
                }
                artworkData.put("firstImage", firstImage);

                recentArtworkList.add(artworkData);
            }

            model.addAttribute("recentArtworks", recentArtworkList);
            
        } catch (Exception e) {
            log.error("加载管理首页数据失败", e);
        }
        
        return "admin/index";
    }

    /**
     * 用户管理页面
     */
    @GetMapping("/users")
    public String users(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            Model model) {
        try {
            Page<User> pageParam = new Page<>(page, size);
            IPage<User> userPage = userService.getAllUsers(pageParam);
            
            model.addAttribute("users", userPage.getRecords());
            model.addAttribute("currentPage", page);
            model.addAttribute("totalPages", userPage.getPages());
            model.addAttribute("total", userPage.getTotal());
            
        } catch (Exception e) {
            log.error("加载用户列表失败", e);
        }
        
        return "admin/users";
    }

    /**
     * 用户详情页面
     */
    @GetMapping("/users/{id}")
    public String userDetail(@PathVariable Long id, Model model) {
        try {
            User user = userService.getUserById(id);
            if (user == null) {
                model.addAttribute("error", "用户不存在");
                return "admin/error";
            }

            // 获取用户的作品列表
            Page<Artwork> artworkPage = new Page<>(1, 10);
            IPage<Artwork> userArtworks = artworkService.getUserArtworks(id, artworkPage);

            // 获取用户的评价统计
            Page<Evaluation> evaluationPage = new Page<>(1, 10);
            IPage<Evaluation> userEvaluations = evaluationService.getUserEvaluations(id, evaluationPage, null);

            // 计算统计数据
            long totalArtworks = artworkService.getUserArtworksCount(id);
            long totalEvaluations = userEvaluations.getTotal();

            // 计算平均分
            double averageScore = 0.0;
            if (!userEvaluations.getRecords().isEmpty()) {
                double totalScore = userEvaluations.getRecords().stream()
                    .filter(e -> e.getOverallScore() != null)
                    .mapToDouble(e -> e.getOverallScore().doubleValue())
                    .sum();
                long scoreCount = userEvaluations.getRecords().stream()
                    .filter(e -> e.getOverallScore() != null)
                    .count();
                if (scoreCount > 0) {
                    averageScore = totalScore / scoreCount;
                }
            }

            model.addAttribute("user", user);
            model.addAttribute("artworks", userArtworks.getRecords());
            model.addAttribute("evaluations", userEvaluations.getRecords());
            model.addAttribute("totalArtworks", totalArtworks);
            model.addAttribute("totalEvaluations", totalEvaluations);
            model.addAttribute("averageScore", Math.round(averageScore * 100.0) / 100.0);

        } catch (Exception e) {
            log.error("加载用户详情失败", e);
            model.addAttribute("error", "加载用户详情失败");
        }

        return "admin/user-detail";
    }

    /**
     * 作品管理页面
     */
    @GetMapping("/artworks")
    public String artworks(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            Model model) {
        try {
            Page<Artwork> pageParam = new Page<>(page, size);
            IPage<Artwork> artworkPage = artworkService.getAllArtworks(pageParam);

            // 为每个作品添加第一张图片URL
            List<Map<String, Object>> artworkList = new ArrayList<>();
            for (Artwork artwork : artworkPage.getRecords()) {
                Map<String, Object> artworkData = new HashMap<>();
                artworkData.put("id", artwork.getId());
                artworkData.put("title", artwork.getTitle());
                artworkData.put("userId", artwork.getUserId());
                artworkData.put("status", artwork.getStatus());
                artworkData.put("createdAt", artwork.getCreatedAt());
                artworkData.put("images", artwork.getImages());

                // 解析第一张图片
                String firstImage = null;
                if (artwork.getImages() != null && !artwork.getImages().isEmpty()) {
                    try {
                        if (artwork.getImages().startsWith("[")) {
                            // JSON数组格式
                            List<String> images = JSONArray.parseArray(artwork.getImages(), String.class);
                            if (!images.isEmpty()) {
                                firstImage = images.get(0);
                            }
                        } else {
                            // 单个URL
                            firstImage = artwork.getImages();
                        }
                    } catch (Exception e) {
                        log.warn("解析作品图片失败: {}", artwork.getId());
                    }
                }
                artworkData.put("firstImage", firstImage);

                artworkList.add(artworkData);
            }

            model.addAttribute("artworks", artworkList);
            model.addAttribute("currentPage", page);
            model.addAttribute("totalPages", artworkPage.getPages());
            model.addAttribute("total", artworkPage.getTotal());

        } catch (Exception e) {
            log.error("加载作品列表失败", e);
        }

        return "admin/artworks";
    }

    /**
     * 作品详情页面
     */
    @GetMapping("/artworks/{id}")
    public String artworkDetail(@PathVariable Long id, Model model) {
        try {
            Artwork artwork = artworkService.getArtworkById(id);
            if (artwork == null) {
                model.addAttribute("error", "作品不存在");
                return "admin/error";
            }

            // 解析图片列表
            List<String> images = new ArrayList<>();
            if (artwork.getImages() != null && !artwork.getImages().isEmpty()) {
                try {
                    if (artwork.getImages().startsWith("[")) {
                        images = JSONArray.parseArray(artwork.getImages(), String.class);
                    } else {
                        images.add(artwork.getImages());
                    }
                } catch (Exception e) {
                    log.warn("解析作品图片失败: {}", artwork.getId());
                }
            }

            // 获取评价列表
            List<Evaluation> evaluations = evaluationService.getArtworkEvaluations(id, null);

            model.addAttribute("artwork", artwork);
            model.addAttribute("images", images);
            model.addAttribute("evaluations", evaluations);

        } catch (Exception e) {
            log.error("加载作品详情失败", e);
            model.addAttribute("error", "加载作品详情失败");
        }

        return "admin/artwork-detail";
    }

    /**
     * 评价管理页面
     */
    @GetMapping("/evaluations")
    public String evaluations(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            Model model) {
        try {
            Page<Evaluation> pageParam = new Page<>(page, size);
            IPage<Evaluation> evaluationPage = evaluationService.getAllEvaluations(pageParam);
            
            model.addAttribute("evaluations", evaluationPage.getRecords());
            model.addAttribute("currentPage", page);
            model.addAttribute("totalPages", evaluationPage.getPages());
            model.addAttribute("total", evaluationPage.getTotal());
            
        } catch (Exception e) {
            log.error("加载评价列表失败", e);
        }
        
        return "admin/evaluations";
    }

    /**
     * 评价详情页面
     */
    @GetMapping("/evaluations/{id}")
    public String evaluationDetail(@PathVariable Long id, Model model) {
        try {
            Evaluation evaluation = evaluationService.getEvaluationById(id);
            if (evaluation == null) {
                model.addAttribute("error", "评价不存在");
                return "admin/error";
            }

            // 获取评分详情
            EvaluationScore score = evaluationService.getEvaluationScore(id);

            // 获取作品信息
            Artwork artwork = null;
            String firstImage = null;
            if (evaluation.getArtworkId() != null) {
                artwork = artworkService.getArtworkById(evaluation.getArtworkId());

                // 解析第一张图片
                if (artwork != null && artwork.getImages() != null && !artwork.getImages().isEmpty()) {
                    try {
                        if (artwork.getImages().startsWith("[")) {
                            // JSON数组格式
                            List<String> images = JSONArray.parseArray(artwork.getImages(), String.class);
                            if (!images.isEmpty()) {
                                firstImage = images.get(0);
                            }
                        } else {
                            // 单个URL
                            firstImage = artwork.getImages();
                        }
                    } catch (Exception e) {
                        log.warn("解析作品图片失败: {}", artwork.getId());
                    }
                }
            }

            // 获取用户信息
            User user = null;
            if (artwork != null && artwork.getUserId() != null) {
                user = userService.getUserById(artwork.getUserId());
            }

            model.addAttribute("evaluation", evaluation);
            model.addAttribute("score", score);
            model.addAttribute("artwork", artwork);
            model.addAttribute("firstImage", firstImage);
            model.addAttribute("user", user);

        } catch (Exception e) {
            log.error("加载评价详情失败", e);
            model.addAttribute("error", "加载评价详情失败");
        }

        return "admin/evaluation-detail";
    }

    /**
     * 系统设置页面
     */
    @GetMapping("/settings")
    public String settings(Model model) {
        try {
            // 获取系统统计信息
            long totalUsers = userService.count();
            long totalArtworks = artworkService.count();
            long totalEvaluations = evaluationService.count();

            // 获取最近7天的数据统计
            // 这里可以添加更详细的统计逻辑

            model.addAttribute("totalUsers", totalUsers);
            model.addAttribute("totalArtworks", totalArtworks);
            model.addAttribute("totalEvaluations", totalEvaluations);

            // 系统配置信息
            model.addAttribute("systemVersion", "1.0.0");
            model.addAttribute("javaVersion", System.getProperty("java.version"));
            model.addAttribute("osName", System.getProperty("os.name"));

        } catch (Exception e) {
            log.error("加载系统设置失败", e);
        }

        return "admin/settings";
    }

    /**
     * API接口 - 获取统计数据
     */
    @GetMapping("/api/stats")
    @ResponseBody
    public Map<String, Object> getStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalUsers", userService.count());
            stats.put("totalArtworks", artworkService.count());
            stats.put("totalEvaluations", evaluationService.count());
            
            result.put("code", 0);
            result.put("data", stats);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            result.put("code", 1);
            result.put("msg", "获取统计数据失败");
        }
        
        return result;
    }

    /**
     * API接口 - 更新用户信息
     */
    @PutMapping("/api/users/{id}")
    @ResponseBody
    public Map<String, Object> updateUser(@PathVariable Long id, @RequestBody Map<String, Object> updateData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取现有用户
            User user = userService.getUserById(id);
            if (user == null) {
                result.put("code", 1);
                result.put("msg", "用户不存在");
                return result;
            }

            // 更新用户信息
            if (updateData.containsKey("nickname")) {
                user.setNickname((String) updateData.get("nickname"));
            }
            if (updateData.containsKey("role")) {
                user.setRole((Integer) updateData.get("role"));
            }
            if (updateData.containsKey("status")) {
                user.setStatus((Integer) updateData.get("status"));
            }
            if (updateData.containsKey("avatar")) {
                user.setAvatar((String) updateData.get("avatar"));
            }

            // 保存更新
            boolean success = userService.updateUser(user);

            if (success) {
                result.put("code", 0);
                result.put("msg", "更新成功");
                log.info("用户 {} 信息更新成功", id);
            } else {
                result.put("code", 1);
                result.put("msg", "更新失败");
            }

        } catch (Exception e) {
            log.error("更新用户失败", e);
            result.put("code", 1);
            result.put("msg", "更新失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * API接口 - 删除用户
     */
    @DeleteMapping("/api/users/{id}")
    @ResponseBody
    public Map<String, Object> deleteUser(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();

        try {
            userService.deleteUser(id);
            result.put("code", 0);
            result.put("msg", "删除成功");
        } catch (Exception e) {
            log.error("删除用户失败", e);
            result.put("code", 1);
            result.put("msg", "删除失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * API接口 - 删除作品
     */
    @DeleteMapping("/api/artworks/{id}")
    @ResponseBody
    public Map<String, Object> deleteArtwork(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();

        try {
            artworkService.deleteArtwork(id);
            result.put("code", 0);
            result.put("msg", "删除成功");
        } catch (Exception e) {
            log.error("删除作品失败", e);
            result.put("code", 1);
            result.put("msg", "删除失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * API接口 - 删除评价
     */
    @DeleteMapping("/api/evaluations/{id}")
    @ResponseBody
    public Map<String, Object> deleteEvaluation(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();

        try {
            evaluationService.deleteEvaluation(id);
            result.put("code", 0);
            result.put("msg", "删除成功");
        } catch (Exception e) {
            log.error("删除评价失败", e);
            result.put("code", 1);
            result.put("msg", "删除失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 教师认证管理页面
     */
    @GetMapping("/teacher-applications")
    public String teacherApplications(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Integer status,
            Model model) {
        try {
            Page<TeacherApplication> pageParam = new Page<>(page, size);
            IPage<TeacherApplication> applicationPage = teacherApplicationService.getApplicationsPage(pageParam, status);

            // 获取统计信息
            Map<String, Object> statistics = teacherApplicationService.getStatusStatistics();

            model.addAttribute("teacherApps", applicationPage.getRecords());
            model.addAttribute("currentPage", page);
            model.addAttribute("totalPages", applicationPage.getPages());
            model.addAttribute("total", applicationPage.getTotal());
            model.addAttribute("status", status);
            model.addAttribute("statistics", statistics);

        } catch (Exception e) {
            log.error("加载教师认证申请列表失败", e);
        }

        return "admin/teacher-applications";
    }

    /**
     * 教师认证申请详情页面
     */
    @GetMapping("/teacher-applications/{id}")
    public String teacherApplicationDetail(@PathVariable Long id, Model model) {
        try {
            TeacherApplication teacherApp = teacherApplicationService.getById(id);
            if (teacherApp == null) {
                model.addAttribute("error", "申请记录不存在");
                return "admin/error";
            }

            // 获取申请人信息
            User user = userService.getUserById(teacherApp.getUserId());

            model.addAttribute("teacherApp", teacherApp);
            model.addAttribute("user", user);

        } catch (Exception e) {
            log.error("加载教师认证申请详情失败", e);
            model.addAttribute("error", "加载失败: " + e.getMessage());
        }

        return "admin/teacher-application-detail";
    }

    /**
     * 审核教师认证申请
     */
    @PostMapping("/teacher-applications/{id}/review")
    @ResponseBody
    public Map<String, Object> reviewTeacherApplication(
            @PathVariable Long id,
            @RequestBody Map<String, Object> reviewRequest) {
        Map<String, Object> result = new HashMap<>();

        try {
            Integer status = (Integer) reviewRequest.get("status");
            String adminRemark = (String) reviewRequest.get("adminRemark");
            Long reviewerId = 1L; // 默认管理员ID

            if (status == null || (status != 1 && status != 2)) {
                throw new RuntimeException("审核状态无效");
            }

            boolean success = teacherApplicationService.reviewApplication(id, status, adminRemark, reviewerId);

            if (success) {
                result.put("code", 0);
                result.put("msg", status == 1 ? "审核通过" : "审核拒绝");
            } else {
                result.put("code", 1);
                result.put("msg", "审核失败");
            }

            log.info("教师认证申请 {} 审核完成，状态: {}", id, status);

        } catch (Exception e) {
            log.error("审核教师认证申请失败", e);
            result.put("code", 1);
            result.put("msg", e.getMessage());
        }

        return result;
    }
}
