package com.meiyuan.artevaluation.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meiyuan.artevaluation.entity.Artwork;
import com.meiyuan.artevaluation.service.ArtworkService;
import com.meiyuan.artevaluation.service.EvaluationService;
import com.meiyuan.artevaluation.service.FileStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/upload")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class UploadController {

    private final FileStorageService fileStorageService;
    private final ArtworkService artworkService;
    private final EvaluationService evaluationService;

    /**
     * 上传图片
     *
     * @param file 图片文件
     * @return 上传结果
     */
    @PostMapping("/image")
    public Map<String, Object> uploadImage(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("接收到图片上传请求，文件名: {}, 大小: {} bytes",
                    file.getOriginalFilename(), file.getSize());

            if (file.isEmpty()) {
                result.put("code", 1);
                result.put("msg", "上传文件不能为空");
                return result;
            }

            String imageUrl = fileStorageService.uploadFile(file);

            result.put("code", 0);
            result.put("msg", "上传成功");
            result.put("data", imageUrl);

            log.info("图片上传成功，URL: {}", imageUrl);
        } catch (IllegalArgumentException e) {
            log.warn("上传参数错误: {}", e.getMessage());
            result.put("code", 1);
            result.put("msg", e.getMessage());
        } catch (Exception e) {
            log.error("上传图片异常", e);
            result.put("code", 1);
            result.put("msg", "上传失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 上传作品
     *
     * @param userId 用户ID
     * @param title 作品标题
     * @param files 图片文件列表
     * @return 上传结果
     */
    @PostMapping("/artwork")
    public Map<String, Object> uploadArtwork(
            @RequestParam("userId") Long userId,
            @RequestParam("title") String title,
            @RequestParam("files") List<MultipartFile> files) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 上传图片
            List<String> imageUrls = new ArrayList<>();
            for (MultipartFile file : files) {
                String imageUrl = fileStorageService.uploadFile(file);
                imageUrls.add(imageUrl);
            }

            // 创建作品记录
            Artwork artwork = new Artwork();
            artwork.setUserId(userId);
            artwork.setTitle(title);
            artwork.setImages(JSONArray.toJSONString(imageUrls));
            artwork.setStatus(1); // 待评价

            Long artworkId = artworkService.createArtwork(artwork);

            // 异步创建AI评价（不阻塞上传响应）
            if (!imageUrls.isEmpty()) {
                String firstImageUrl = imageUrls.get(0);
                // 使用异步方式调用AI评价，避免阻塞用户上传
                new Thread(() -> {
                    try {
                        evaluationService.createAiEvaluation(artworkId, firstImageUrl);
                        log.info("AI评价创建成功，作品ID: {}", artworkId);
                    } catch (Exception e) {
                        log.error("AI评价创建失败，作品ID: {}", artworkId, e);
                    }
                }).start();
            }

            result.put("code", 0);
            result.put("msg", "上传成功");
            result.put("data", artworkId);
        } catch (Exception e) {
            log.error("上传作品异常", e);
            result.put("code", 1);
            result.put("msg", "上传失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 批量上传图片（支持多张图片同时上传）
     *
     * @param files 图片文件列表
     * @return 上传结果
     */
    @PostMapping("/images")
    public Map<String, Object> uploadImages(@RequestParam("files") List<MultipartFile> files) {
        Map<String, Object> result = new HashMap<>();

        try {
            List<String> imageUrls = new ArrayList<>();
            for (MultipartFile file : files) {
                String imageUrl = fileStorageService.uploadFile(file);
                imageUrls.add(imageUrl);
            }

            result.put("code", 0);
            result.put("msg", "上传成功");
            result.put("data", imageUrls);
        } catch (Exception e) {
            log.error("批量上传图片异常", e);
            result.put("code", 1);
            result.put("msg", "上传失败: " + e.getMessage());
        }

        return result;
    }
} 