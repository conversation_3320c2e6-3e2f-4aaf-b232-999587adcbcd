<view class="certification-container">
  <!-- 加载状态 -->
  <t-loading wx:if="{{loading}}" theme="circular" size="40rpx" text="加载中..." />
  
  <!-- 已有申请状态显示 -->
  <view wx:if="{{!loading && applicationStatus}}" class="status-card">
    <view class="status-header">
      <t-icon name="info-circle" size="40rpx" color="#0052d9" />
      <text class="status-title">申请状态</text>
    </view>
    
    <view class="status-content">
      <view class="status-item">
        <text class="label">申请时间：</text>
        <text class="value">{{applicationStatus.createdAt}}</text>
      </view>
      
      <view class="status-item">
        <text class="label">当前状态：</text>
        <t-tag 
          wx:if="{{applicationStatus.status === 0}}" 
          theme="warning" 
          variant="light"
        >
          待审核
        </t-tag>
        <t-tag 
          wx:if="{{applicationStatus.status === 1}}" 
          theme="success" 
          variant="light"
        >
          审核通过
        </t-tag>
        <t-tag 
          wx:if="{{applicationStatus.status === 2}}" 
          theme="danger" 
          variant="light"
        >
          审核拒绝
        </t-tag>
      </view>
      
      <view wx:if="{{applicationStatus.adminRemark}}" class="status-item">
        <text class="label">审核备注：</text>
        <text class="value">{{applicationStatus.adminRemark}}</text>
      </view>
    </view>
  </view>

  <!-- 申请表单 -->
  <view wx:if="{{!loading && canApply}}" class="form-container">
    <view class="form-header">
      <text class="form-title">教师认证申请</text>
      <text class="form-desc">请填写真实信息，我们将在3个工作日内完成审核</text>
    </view>

    <t-cell-group theme="card">
      <!-- 基本信息 -->
      <t-cell title="基本信息" class="section-header" />
      
      <t-cell title="真实姓名" required>
        <t-input
          slot="note"
          placeholder="请输入真实姓名"
          value="{{formData.realName}}"
          data-field="realName"
          bind:change="onInputChange"
        />
      </t-cell>
      
      <t-cell title="身份证号" required>
        <t-input
          slot="note"
          placeholder="请输入身份证号"
          value="{{formData.idCard}}"
          data-field="idCard"
          bind:change="onInputChange"
        />
      </t-cell>
      
      <t-cell title="工作单位" required>
        <t-input
          slot="note"
          placeholder="请输入工作单位"
          value="{{formData.workUnit}}"
          data-field="workUnit"
          bind:change="onInputChange"
        />
      </t-cell>
      
      <t-cell title="职位" required>
        <t-input
          slot="note"
          placeholder="请输入职位"
          value="{{formData.position}}"
          data-field="position"
          bind:change="onInputChange"
        />
      </t-cell>
      
      <!-- 联系方式 -->
      <t-cell title="联系方式" class="section-header" />
      
      <t-cell title="联系电话" required>
        <t-input
          slot="note"
          placeholder="请输入手机号"
          type="number"
          value="{{formData.phone}}"
          data-field="phone"
          bind:change="onInputChange"
        />
      </t-cell>
      
      <t-cell title="邮箱">
        <t-input
          slot="note"
          placeholder="请输入邮箱（选填）"
          value="{{formData.email}}"
          data-field="email"
          bind:change="onInputChange"
        />
      </t-cell>
    </t-cell-group>

    <!-- 认证材料 -->
    <view class="upload-section">
      <view class="section-title">
        <text class="title-text">认证材料</text>
        <text class="title-required">*</text>
      </view>
      <view class="upload-desc">
        请上传教师资格证、工作证明等相关材料（最多3张）
      </view>
      
      <view class="upload-container">
        <!-- 已上传的图片 -->
        <view 
          wx:for="{{formData.certificateImages}}" 
          wx:key="index" 
          class="image-item"
        >
          <image 
            src="{{item}}" 
            class="uploaded-image"
            data-url="{{item}}"
            bind:tap="previewImage"
          />
          <view 
            class="delete-btn"
            data-index="{{index}}"
            bind:tap="deleteImage"
          >
            <t-icon name="close" size="24rpx" color="#fff" />
          </view>
        </view>
        
        <!-- 上传按钮 -->
        <view 
          wx:if="{{formData.certificateImages.length < 3}}"
          class="upload-btn"
          bind:tap="uploadCertificate"
        >
          <t-icon name="add" size="48rpx" color="#c5c5c5" />
          <text class="upload-text">上传图片</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-container">
      <t-button
        theme="primary"
        size="large"
        block
        loading="{{submitting}}"
        bind:tap="submitApplication"
      >
        提交申请
      </t-button>
    </view>
  </view>

  <!-- 无法申请提示 -->
  <view wx:if="{{!loading && !canApply && !applicationStatus}}" class="no-apply-tip">
    <t-icon name="info-circle" size="80rpx" color="#c5c5c5" />
    <text class="tip-text">您已经是教师或有待审核的申请</text>
  </view>
</view>

<t-toast id="t-toast" />
