<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 美院作品评价管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --sidebar-width: 250px;
            --header-height: 60px;
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: block;
            padding: 15px 25px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: var(--accent-color);
        }

        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }

        .header {
            background: white;
            height: var(--header-height);
            padding: 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .content {
            padding: 30px;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #eee;
            font-weight: 600;
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--primary-color);
        }

        .page-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 30px;
        }



        .pagination {
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>管理系统</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="/admin"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/users" class="active"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/artworks"><i class="fas fa-palette"></i> 作品管理</a></li>
            <li><a href="/admin/evaluations"><i class="fas fa-star"></i> 评价管理</a></li>
            <li><a href="/admin/teacher-applications"><i class="fas fa-user-graduate"></i> 教师认证</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
        </ul>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 头部 -->
        <div class="header">
            <h5 class="mb-0">用户管理</h5>
            <div class="d-flex align-items-center">
                <span class="text-muted me-3">美院作品评价管理系统</span>
                <a href="/admin/logout" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <h2 class="page-title">用户管理</h2>
            
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">用户列表</h5>
                    <div>
                        <span class="text-muted">共 <span th:text="${total}">0</span> 个用户</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>昵称</th>
                                    <th>OpenID</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="user : ${users}">
                                    <td th:text="${user.id}">1</td>
                                    <td th:text="${user.nickname}">用户昵称</td>
                                    <td>
                                        <code th:text="${#strings.abbreviate(user.openid, 20)}">openid</code>
                                    </td>
                                    <td>
                                        <span th:if="${user.role == 1}" class="badge bg-primary">学生</span>
                                        <span th:if="${user.role == 2}" class="badge bg-success">教师</span>
                                        <span th:if="${user.role == 3}" class="badge bg-danger">管理员</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">正常</span>
                                    </td>
                                    <td th:text="${#temporals.format(user.createdAt, 'yyyy-MM-dd HH:mm')}">2023-12-01 10:00</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1"
                                                th:data-user-id="${user.id}"
                                                onclick="editUser(this.dataset.userId)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger"
                                                th:data-user-id="${user.id}"
                                                th:data-user-nickname="${user.nickname}"
                                                onclick="deleteUser(this.dataset.userId, this.dataset.userNickname)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr th:if="${#lists.isEmpty(users)}">
                                    <td colspan="7" class="text-center text-muted py-4">暂无用户数据</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <nav th:if="${totalPages > 1}" class="mt-4">
                <ul class="pagination">
                    <li class="page-item" th:classappend="${currentPage == 1} ? 'disabled'">
                        <a class="page-link" th:href="@{/admin/users(page=${currentPage - 1})}">上一页</a>
                    </li>
                    
                    <li th:each="i : ${#numbers.sequence(1, totalPages)}" 
                        class="page-item" 
                        th:classappend="${i == currentPage} ? 'active'">
                        <a class="page-link" th:href="@{/admin/users(page=${i})}" th:text="${i}">1</a>
                    </li>
                    
                    <li class="page-item" th:classappend="${currentPage == totalPages} ? 'disabled'">
                        <a class="page-link" th:href="@{/admin/users(page=${currentPage + 1})}">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        function editUser(userId) {
            // 跳转到用户详情页面
            window.location.href = '/admin/users/' + userId;
        }

        function deleteUser(userId, nickname) {
            if (confirm('确定要删除用户 "' + nickname + '" 吗？此操作不可恢复！')) {
                $.ajax({
                    url: '/admin/api/users/' + userId,
                    method: 'DELETE',
                    success: function(response) {
                        if (response.code === 0) {
                            alert('删除成功');
                            location.reload();
                        } else {
                            alert('删除失败: ' + response.msg);
                        }
                    },
                    error: function() {
                        alert('删除失败，请重试');
                    }
                });
            }
        }
    </script>
</body>
</html>
