@import '/variable.less';

.home-navbar {
  position: relative;

  .t-navbar {
    &__left {
      margin-left: 12rpx;
    }
  }

  &__icon {
    padding: 12rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }
  }

  &__left {
    display: flex;
    align-items: center;
    padding: 0 16rpx;

    .t-search {
      --td-search-height: 64rpx;
      --td-search-font-size: @font-size-mini;
      width: 375rpx;

      .t-icon {
        font-size: @font-size-default !important;
      }
    }
  }

  // 高级搜索面板样式
  .advanced-search-panel {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border-radius: 0 0 24rpx 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    z-index: 1000;
    animation: slideDown 0.3s ease-out;
  }

  .advanced-search-content {
    padding: 32rpx 24rpx;
  }

  .search-filter-item {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .filter-label {
      width: 120rpx;
      font-size: 28rpx;
      color: #333;
      margin-right: 16rpx;
    }

    .t-dropdown-menu {
      flex: 1;
    }
  }

  .search-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16rpx;
    margin-top: 32rpx;
    padding-top: 24rpx;
    border-top: 1rpx solid #f0f0f0;
  }

  // 遮罩层
  .search-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
    animation: fadeIn 0.3s ease-out;
  }

  // 动画效果
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-20rpx);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}
