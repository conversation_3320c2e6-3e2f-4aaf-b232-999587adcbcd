import Toast from 'tdesign-miniprogram/toast/index';
import Message from 'tdesign-miniprogram/message/index';
import request from '~/api/request';

const app = getApp();

Page({
  data: {
    logging: false,
    testing: false,
  },

  // 微信一键登录
  async wxLogin() {
    this.setData({ logging: true });

    try {
      // 获取微信登录code
      console.log('开始获取微信登录code...');
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        });
      });

      console.log('微信登录code获取结果:', loginRes);
      if (!loginRes.code) {
        throw new Error('获取登录凭证失败');
      }

      // 调用后端微信登录接口
      console.log('准备调用后端登录接口，code:', loginRes.code);
      const result = await request(`/auth/wx-login?code=${loginRes.code}`, 'GET');

      console.log('后端登录接口返回结果:', result);

      if (result.code === 0) {
        console.log('登录成功，用户信息:', result.data.userInfo);
        console.log('登录成功，token:', result.data.token);

        // 使用全局登录状态管理
        app.setLoginStatus(result.data.userInfo, result.data.token);

        Toast({
          context: this,
          selector: '#t-toast',
          message: '登录成功',
          theme: 'success',
        });

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>',
          });
        }, 1500);
      } else {
        console.error('登录失败，错误信息:', result.msg);
        throw new Error(result.msg || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      Toast({
        context: this,
        selector: '#t-toast',
        message: error.message || '登录失败，请重试',
        theme: 'error',
      });
    } finally {
      this.setData({ logging: false });
    }
  },

  // 测试API连接
  async testApi() {
    this.setData({ testing: true });

    try {
      console.log('开始测试API连接...');
      const result = await request('/auth/wx-login?code=test123', 'GET');
      console.log('API测试结果:', result);

      Toast({
        context: this,
        selector: '#t-toast',
        message: 'API连接成功',
        theme: 'success',
      });
    } catch (error) {
      console.error('API测试失败:', error);
      Toast({
        context: this,
        selector: '#t-toast',
        message: 'API连接失败: ' + (error.message || '未知错误'),
        theme: 'error',
      });
    } finally {
      this.setData({ testing: false });
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '美院作品评价系统',
      path: '/pages/home/<USER>'
    };
  }
});
