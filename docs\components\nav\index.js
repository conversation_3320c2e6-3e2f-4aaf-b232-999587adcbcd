Component({
  options: {
    styleIsolation: 'shared',
  },
  properties: {
    navType: {
      type: String,
      value: 'title',
    },
    titleText: String,
    // 当前激活的tab
    activeTab: {
      type: String,
      value: 'recommend',
    },
  },
  data: {
    showAdvancedSearch: false,
    searchValue: '',
    searchPlaceholder: '请搜索你想要的内容',
    showAdvancedFilter: true, // 是否显示高级筛选按钮
    selectedType: '',
    selectedStatus: '',
    selectedTime: '',
    typeOptions: [
      { label: '全部类型', value: '' },
      { label: '素描', value: 'sketch' },
      { label: '色彩', value: 'color' },
      { label: '速写', value: 'quick' },
      { label: '设计', value: 'design' },
    ],
    statusOptions: [
      { label: '全部状态', value: '' },
      { label: '待评价', value: '1' },
      { label: '已评价', value: '2' },
    ],
    timeOptions: [
      { label: '全部时间', value: '' },
      { label: '今天', value: 'today' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
    ],
    statusHeight: 0,
  },
  observers: {
    'activeTab': function(activeTab) {
      // 根据当前tab更新搜索框状态
      if (activeTab === 'recommend') {
        this.setData({
          searchPlaceholder: '搜索推荐作品...',
          showAdvancedFilter: true,
        });
      } else if (activeTab === 'upload') {
        this.setData({
          searchPlaceholder: '搜索我的作品...',
          showAdvancedFilter: false,
          showAdvancedSearch: false, // 切换到我的上传时关闭高级搜索面板
        });
      }
    }
  },

  lifetimes: {
    ready() {
      const statusHeight = wx.getWindowInfo().statusBarHeight;
      this.setData({ statusHeight });
    },
  },
  methods: {
    // 搜索输入框内容变化
    onSearchChange(e) {
      this.setData({
        searchValue: e.detail.value,
      });
    },

    // 搜索提交
    onSearchSubmit(e) {
      const searchValue = e.detail.value.trim();
      this.performSearch(searchValue);
    },

    // 清空搜索
    onSearchClear() {
      this.setData({
        searchValue: '',
      });
      this.performSearch('');
    },

    // 执行搜索
    performSearch(keyword) {
      const searchData = {
        keyword: keyword,
        type: this.data.selectedType,
        status: this.data.selectedStatus,
        time: this.data.selectedTime,
        tab: this.data.activeTab,
      };

      // 触发搜索事件
      this.triggerEvent('search-change', searchData);
    },

    // 打开高级搜索面板
    openAdvancedSearch() {
      if (!this.data.showAdvancedFilter) return;

      this.setData({
        showAdvancedSearch: !this.data.showAdvancedSearch,
      });
    },

    // 关闭高级搜索面板
    closeAdvancedSearch() {
      this.setData({
        showAdvancedSearch: false,
      });
    },

    // 作品类型筛选
    onTypeChange(e) {
      this.setData({
        selectedType: e.detail.value,
      });
    },

    // 评价状态筛选
    onStatusChange(e) {
      this.setData({
        selectedStatus: e.detail.value,
      });
    },

    // 时间筛选
    onTimeChange(e) {
      this.setData({
        selectedTime: e.detail.value,
      });
    },

    // 重置筛选条件
    resetFilters() {
      this.setData({
        selectedType: '',
        selectedStatus: '',
        selectedTime: '',
      });
    },

    // 应用筛选条件
    applyFilters() {
      const searchData = {
        keyword: this.data.searchValue,
        type: this.data.selectedType,
        status: this.data.selectedStatus,
        time: this.data.selectedTime,
        tab: this.data.activeTab,
      };

      // 触发搜索事件（包含筛选条件）
      this.triggerEvent('search-change', searchData);

      // 关闭高级搜索面板
      this.setData({
        showAdvancedSearch: false,
      });
    },


  },
});
