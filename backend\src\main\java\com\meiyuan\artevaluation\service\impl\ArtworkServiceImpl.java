package com.meiyuan.artevaluation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.Artwork;
import com.meiyuan.artevaluation.mapper.ArtworkMapper;
import com.meiyuan.artevaluation.service.ArtworkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 作品服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArtworkServiceImpl implements ArtworkService {

    private final ArtworkMapper artworkMapper;

    @Override
    public Long createArtwork(Artwork artwork) {
        artworkMapper.insert(artwork);
        return artwork.getId();
    }

    @Override
    public Artwork getArtworkById(Long id) {
        return artworkMapper.selectById(id);
    }

    @Override
    public boolean deleteArtworkById(Long id) {
        return artworkMapper.deleteById(id) > 0;
    }


    @Override
    public IPage<Artwork> getUserArtworks(Long userId, Page<Artwork> page) {
        LambdaQueryWrapper<Artwork> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Artwork::getUserId, userId)
                .orderByDesc(Artwork::getCreatedAt);
        return artworkMapper.selectPage(page, queryWrapper);
    }

    @Override
    public IPage<Artwork> getAllArtworks(Page<Artwork> page) {
        LambdaQueryWrapper<Artwork> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(Artwork::getCreatedAt);
        return artworkMapper.selectPage(page, queryWrapper);
    }

    @Override
    public boolean updateArtworkStatus(Long id, Integer status) {
        Artwork artwork = new Artwork();
        artwork.setId(id);
        artwork.setStatus(status);
        return artworkMapper.updateById(artwork) > 0;
    }

    @Override
    public long count() {
        return artworkMapper.selectCount(null);
    }

    @Override
    public boolean deleteArtwork(Long id) {
        return artworkMapper.deleteById(id) > 0;
    }

    @Override
    public IPage<Artwork> getAllArtworksWithFilter(Page<Artwork> page, String keyword, String type, String status, String time) {
        LambdaQueryWrapper<Artwork> queryWrapper = new LambdaQueryWrapper<>();

        // 基础条件：未删除
        queryWrapper.eq(Artwork::getDeleted, 0);

        // 关键词搜索（搜索标题和描述）
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper ->
                wrapper.like(Artwork::getTitle, keyword.trim())
                     
            );
        }

        // 状态筛选
        if (status != null && !status.isEmpty()) {
            queryWrapper.eq(Artwork::getStatus, Integer.parseInt(status));
        }

        // 时间筛选
        if (time != null && !time.isEmpty()) {
            LocalDateTime now = LocalDateTime.now();
            switch (time) {
                case "today":
                    queryWrapper.ge(Artwork::getCreatedAt, now.toLocalDate().atStartOfDay());
                    break;
                case "week":
                    queryWrapper.ge(Artwork::getCreatedAt, now.minusWeeks(1));
                    break;
                case "month":
                    queryWrapper.ge(Artwork::getCreatedAt, now.minusMonths(1));
                    break;
            }
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Artwork::getCreatedAt);

        return artworkMapper.selectPage(page, queryWrapper);
    }

    @Override
    public IPage<Artwork> getUserArtworksWithFilter(Long userId, Page<Artwork> page, String keyword, String status, String time) {
        LambdaQueryWrapper<Artwork> queryWrapper = new LambdaQueryWrapper<>();

        // 基础条件：指定用户
        queryWrapper.eq(Artwork::getUserId, userId);

        // 关键词搜索（搜索标题和描述）
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper ->
                wrapper.like(Artwork::getTitle, keyword.trim())

            );
        }

        // 状态筛选
        if (status != null && !status.isEmpty()) {
            queryWrapper.eq(Artwork::getStatus, Integer.parseInt(status));
        }

        // 时间筛选
        if (time != null && !time.isEmpty()) {
            LocalDateTime now = LocalDateTime.now();
            switch (time) {
                case "today":
                    queryWrapper.ge(Artwork::getCreatedAt, now.toLocalDate().atStartOfDay());
                    break;
                case "week":
                    queryWrapper.ge(Artwork::getCreatedAt, now.minusWeeks(1));
                    break;
                case "month":
                    queryWrapper.ge(Artwork::getCreatedAt, now.minusMonths(1));
                    break;
            }
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Artwork::getCreatedAt);

        return artworkMapper.selectPage(page, queryWrapper);
    }

    @Override
    public long getUserArtworksCount(Long userId) {
        LambdaQueryWrapper<Artwork> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Artwork::getUserId, userId);
        return artworkMapper.selectCount(queryWrapper);
    }
}