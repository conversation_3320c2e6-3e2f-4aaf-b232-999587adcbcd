// app.js
import config from './config';
import Mo<PERSON> from './mock/index';
import createBus from './utils/eventBus';
import { connectSocket, fetchUnreadNum } from './mock/chat';

if (config.isMock) {
  Mock();
}

App({
  onLaunch() {
    // 设置API基础URL
    wx.setStorageSync('baseUrl', config.baseUrl);

    // 检查登录状态
    this.checkLoginStatus();

    const updateManager = wx.getUpdateManager();

    updateManager.onCheckForUpdate((res) => {
      // console.log(res.hasUpdate)
    });

    updateManager.onUpdateReady(() => {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success(res) {
          if (res.confirm) {
            updateManager.applyUpdate();
          }
        },
      });
    });

    this.getUnreadNum();
    this.connect();
  },
  globalData: {
    userInfo: null,
    unreadNum: 0, // 未读消息数量
    socket: null, // SocketTask 对象
    isLoggedIn: false, // 登录状态
  },

  /** 全局事件总线 */
  eventBus: createBus(),

  /** 初始化WebSocket */
  connect() {
    const socket = connectSocket();
    socket.onMessage((data) => {
      data = JSON.parse(data);
      if (data.type === 'message' && !data.data.message.read) this.setUnreadNum(this.globalData.unreadNum + 1);
    });
    this.globalData.socket = socket;
  },

  /** 获取未读消息数量 */
  getUnreadNum() {
    fetchUnreadNum().then(({ data }) => {
      this.globalData.unreadNum = data;
      this.eventBus.emit('unread-num-change', data);
    });
  },

  /** 设置未读消息数量 */
  setUnreadNum(unreadNum) {
    this.globalData.unreadNum = unreadNum;
    this.eventBus.emit('unread-num-change', unreadNum);
  },

  /** 检查登录状态 */
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('access_token');

    if (userInfo && token) {
      this.globalData.userInfo = userInfo;
      this.globalData.isLoggedIn = true;
      console.log('用户已登录:', userInfo);
    } else {
      this.globalData.userInfo = null;
      this.globalData.isLoggedIn = false;
      console.log('用户未登录');
    }

    // 触发登录状态变化事件
    this.eventBus.emit('login-status-change', this.globalData.isLoggedIn);
  },

  /** 设置登录状态 */
  setLoginStatus(userInfo, token) {
    if (userInfo && token) {
      // 保存到本地存储
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('access_token', token);

      // 更新全局状态
      this.globalData.userInfo = userInfo;
      this.globalData.isLoggedIn = true;

      console.log('登录成功，用户信息已保存:', userInfo);
    } else {
      // 清除登录信息
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('access_token');

      // 更新全局状态
      this.globalData.userInfo = null;
      this.globalData.isLoggedIn = false;

      console.log('已清除登录信息');
    }

    // 触发登录状态变化事件
    this.eventBus.emit('login-status-change', this.globalData.isLoggedIn);
  },

  /** 退出登录 */
  logout() {
    this.setLoginStatus(null, null);

    // 跳转到登录页面
    wx.reLaunch({
      url: '/pages/login/login'
    });
  },

  /** 检查是否需要登录 */
  requireLogin() {
    if (!this.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再使用此功能',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login',
            });
          }
        }
      });
      return false;
    }
    return true;
  },
});
