package com.meiyuan.artevaluation.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 教师认证申请实体类
 */
@Data
@TableName("teacher_applications")
public class TeacherApplication {

    /**
     * 申请ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 职位
     */
    private String position;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 认证材料图片（JSON数组）
     */
    private String certificateImages;

    /**
     * 申请状态：0-待审核，1-审核通过，2-审核拒绝
     */
    private Integer status;

    /**
     * 管理员备注
     */
    private String adminRemark;

    /**
     * 申请时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 审核时间
     */
    private LocalDateTime reviewedAt;

    /**
     * 审核人ID
     */
    private Long reviewedBy;

    /**
     * 申请状态枚举
     */
    public enum Status {
        PENDING(0, "待审核"),
        APPROVED(1, "审核通过"),
        REJECTED(2, "审核拒绝");

        private final int code;
        private final String desc;

        Status(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static Status fromCode(int code) {
            for (Status status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return null;
        }
    }
}
