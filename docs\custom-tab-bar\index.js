const app = getApp();

Component({
  data: {
    value: '', // 初始值设置为空，避免第一次加载时闪烁
    unreadNum: 0, // 未读消息数量
    list: [
      {
        icon: 'home',
        value: 'home',
        label: '首页',
      },
      {
        icon: 'user',
        value: 'my',
        label: '我的',
      },
    ],
  },
  lifetimes: {
    ready() {
      const pages = getCurrentPages();
      const curPage = pages[pages.length - 1];
      if (curPage) {
        const nameRe = /pages\/(\w+)\/index/.exec(curPage.route);
        if (nameRe === null) return;
        if (nameRe[1] && nameRe) {
          let value = nameRe[1];
          // 处理首页路由
          if (value === 'index') {
            value = 'home';
          }
          this.setData({
            value: value,
          });
        }
      }

      // 移除消息相关功能
    },
  },
  methods: {
    handleChange(e) {
      const { value } = e.detail;
      let url = `/pages/${value}/index`;
      // 处理首页路由
      if (value === 'home') {
        url = '/pages/home/<USER>';
      }
      wx.switchTab({ url });
    },
  },
});
