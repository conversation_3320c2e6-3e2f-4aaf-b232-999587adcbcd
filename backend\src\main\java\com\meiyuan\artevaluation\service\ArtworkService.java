package com.meiyuan.artevaluation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.Artwork;

import java.util.List;

/**
 * 作品服务接口
 */
public interface ArtworkService {

    /**
     * 创建作品
     *
     * @param artwork 作品信息
     * @return 作品ID
     */
    Long createArtwork(Artwork artwork);

    /**
     * 获取作品详情
     *
     * @param id 作品ID
     * @return 作品信息
     */
    Artwork getArtworkById(Long id);

    boolean deleteArtworkById(Long id);


    /**
     * 分页查询用户作品
     *
     * @param userId 用户ID
     * @param page 分页参数
     * @return 作品列表
     */
    IPage<Artwork> getUserArtworks(Long userId, Page<Artwork> page);

    /**
     * 分页查询所有作品
     *
     * @param page 分页参数
     * @return 作品列表
     */
    IPage<Artwork> getAllArtworks(Page<Artwork> page);

    /**
     * 分页查询所有作品（带搜索和筛选条件）
     *
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param type 作品类型
     * @param status 评价状态
     * @param time 时间筛选
     * @return 作品列表
     */
    IPage<Artwork> getAllArtworksWithFilter(Page<Artwork> page, String keyword, String type, String status, String time);

    /**
     * 更新作品状态
     *
     * @param id 作品ID
     * @param status 状态：1-待评价，2-已评价
     * @return 是否成功
     */
    boolean updateArtworkStatus(Long id, Integer status);

    /**
     * 获取作品总数
     *
     * @return 作品总数
     */
    long count();

    /**
     * 删除作品
     *
     * @param id 作品ID
     * @return 是否成功
     */
    boolean deleteArtwork(Long id);

    /**
     * 分页查询用户作品（带搜索和筛选条件）
     *
     * @param userId 用户ID
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param status 评价状态
     * @param time 时间筛选
     * @return 作品列表
     */
    IPage<Artwork> getUserArtworksWithFilter(Long userId, Page<Artwork> page, String keyword, String status, String time);

    /**
     * 获取用户作品数量
     *
     * @param userId 用户ID
     * @return 作品数量
     */
    long getUserArtworksCount(Long userId);
}