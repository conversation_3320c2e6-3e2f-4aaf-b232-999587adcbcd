<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="88db6e39-3a9a-46ff-b328-0a7f12f1678b" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://D:/Program Files (x86)/maven/mavenjar/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar!/com/zaxxer/hikari/HikariConfig.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/Program Files (x86)/maven/mavenjar/org/springframework/boot/spring-boot-autoconfigure/3.1.0/spring-boot-autoconfigure-3.1.0.jar!/org/springframework/boot/autoconfigure/thymeleaf/ThymeleafProperties.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/Program Files (x86)/maven/mavenjar/org/springframework/spring-expression/6.0.9/spring-expression-6.0.9.jar!/org/springframework/expression/spel/SpelEvaluationException.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Program Files (x86)\maven\apache-maven-3.5.3" />
        <option name="localRepository" value="D:\Program Files (x86)\maven\mavenjar" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\Program Files (x86)\maven\apache-maven-3.5.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="302eKV6OqNBlFGCuqkZdZuhU2t3" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.art-evaluation [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.art-evaluation [package].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ArtEvaluationApplication.executor&quot;: &quot;Debug&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/project_work/fhwl/wx_meiyuan_project/wx_meiyuan_project/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="ArtEvaluationApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="art-evaluation" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.meiyuan.artevaluation.ArtEvaluationApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="88db6e39-3a9a-46ff-b328-0a7f12f1678b" name="更改" comment="" />
      <created>1752830030664</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752830030664</updated>
      <workItem from="1752830031856" duration="2735000" />
      <workItem from="1752835827095" duration="1530000" />
      <workItem from="1753064636107" duration="1336000" />
      <workItem from="1753156606920" duration="1242000" />
      <workItem from="1753237863837" duration="614000" />
      <workItem from="1753329340107" duration="8452000" />
      <workItem from="1753429724390" duration="676000" />
      <workItem from="1753691476128" duration="11239000" />
      <workItem from="1753935730941" duration="978000" />
      <workItem from="1753938081549" duration="3637000" />
      <workItem from="1754273400528" duration="3155000" />
      <workItem from="1754358630465" duration="11330000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>