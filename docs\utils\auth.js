/**
 * 认证相关工具函数
 */

// 获取app实例
const getAppInstance = () => {
  try {
    return getApp();
  } catch (e) {
    console.error('无法获取app实例:', e);
    return null;
  }
};

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isLoggedIn = () => {
  const app = getAppInstance();
  if (app) {
    return app.globalData.isLoggedIn;
  }
  
  // 兜底检查本地存储
  const userInfo = wx.getStorageSync('userInfo');
  const token = wx.getStorageSync('access_token');
  return !!(userInfo && token);
};

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息
 */
export const getCurrentUser = () => {
  const app = getAppInstance();
  if (app) {
    return app.globalData.userInfo;
  }
  
  // 兜底从本地存储获取
  return wx.getStorageSync('userInfo') || null;
};

/**
 * 获取当前用户token
 * @returns {string|null} token
 */
export const getCurrentToken = () => {
  return wx.getStorageSync('access_token') || null;
};

/**
 * 要求用户登录，如果未登录则显示登录提示
 * @param {string} message 提示消息
 * @returns {boolean} 是否已登录
 */
export const requireLogin = (message = '请先登录后再使用此功能') => {
  const app = getAppInstance();
  if (app && app.requireLogin) {
    return app.requireLogin();
  }
  
  // 兜底处理
  if (!isLoggedIn()) {
    wx.showModal({
      title: '提示',
      content: message,
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login',
          });
        }
      }
    });
    return false;
  }
  return true;
};

/**
 * 退出登录
 */
export const logout = () => {
  const app = getAppInstance();
  if (app && app.logout) {
    app.logout();
  } else {
    // 兜底处理
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('access_token');
    wx.reLaunch({
      url: '/pages/login/login'
    });
  }
};

/**
 * 检查用户是否有特定角色
 * @param {number} role 角色ID (1: 学生, 2: 教师)
 * @returns {boolean} 是否有该角色
 */
export const hasRole = (role) => {
  const user = getCurrentUser();
  return user && user.role === role;
};

/**
 * 检查用户是否是教师
 * @returns {boolean} 是否是教师
 */
export const isTeacher = () => {
  return hasRole(2);
};

/**
 * 检查用户是否是学生
 * @returns {boolean} 是否是学生
 */
export const isStudent = () => {
  return hasRole(1);
};

/**
 * 页面混入对象，提供登录状态检查功能
 */
export const authMixin = {
  onShow() {
    // 检查登录状态
    const app = getAppInstance();
    if (app && app.checkLoginStatus) {
      app.checkLoginStatus();
    }
  },
  
  methods: {
    // 检查是否已登录
    isLoggedIn,
    
    // 获取当前用户
    getCurrentUser,
    
    // 要求登录
    requireLogin,
    
    // 退出登录
    logout,
    
    // 检查角色
    hasRole,
    isTeacher,
    isStudent,
  }
};

export default {
  isLoggedIn,
  getCurrentUser,
  getCurrentToken,
  requireLogin,
  logout,
  hasRole,
  isTeacher,
  isStudent,
  authMixin
};
