<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评价详情 - 美院艺术作品评价系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .evaluation-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .ai-evaluation {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .teacher-evaluation {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            margin: 0 auto;
        }
        .score-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .artwork-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .content-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #333;
            border-bottom: 2px solid #f8f9fa;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- 返回按钮 -->
            <div class="col-12 mb-3">
                <a href="/admin/evaluations" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回评价列表
                </a>
                <button class="btn btn-outline-secondary ms-2" onclick="history.back()">
                    <i class="fas fa-undo"></i> 返回上页
                </button>
            </div>

            <!-- 评价头部信息 -->
            <div class="col-12">
                <div class="evaluation-header" 
                     th:classappend="${evaluation.type == 1} ? 'ai-evaluation' : 'teacher-evaluation'">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-3">
                                <i th:if="${evaluation.type == 1}" class="fas fa-robot fa-2x me-3"></i>
                                <i th:if="${evaluation.type == 2}" class="fas fa-user-tie fa-2x me-3"></i>
                                <div>
                                    <h2 class="mb-0">
                                        <span th:if="${evaluation.type == 1}">AI智能评价</span>
                                        <span th:if="${evaluation.type == 2}">教师专业评价</span>
                                    </h2>
                                    <p class="mb-0 opacity-75">
                                        评价ID: <span th:text="${evaluation.id}">1</span> | 
                                        评价时间: <span th:text="${#temporals.format(evaluation.createdAt, 'yyyy-MM-dd HH:mm:ss')}">2023-12-01 10:00:00</span>
                                    </p>
                                </div>
                            </div>
                            
                            <!-- 作品和用户信息 -->
                            <div th:if="${artwork}" class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><i class="fas fa-palette me-2"></i>作品: <span th:text="${artwork.title}">作品标题</span></p>
                                </div>
                                <div class="col-md-6" th:if="${user}">
                                    <p class="mb-1"><i class="fas fa-user me-2"></i>作者: <span th:text="${user.nickname}">用户昵称</span></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 总分显示 -->
                        <div class="col-md-4 text-center" th:if="${evaluation.overallScore}">
                            <div class="score-circle">
                                <span th:text="${evaluation.overallScore}">85</span>
                            </div>
                            <p class="mt-2 mb-0">总分</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细评分 -->
            <div class="col-md-6" th:if="${score}">
                <div class="content-section">
                    <h5 class="section-title">
                        <i class="fas fa-chart-bar text-primary"></i> 详细评分
                    </h5>
                    
                    <div class="score-item" th:if="${score.perspectiveScore}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-cube text-info me-2"></i>透视准确性</span>
                            <span class="badge bg-primary fs-6" th:text="${score.perspectiveScore} + '分'">18分</span>
                        </div>
                    </div>
                    
                    <div class="score-item" th:if="${score.proportionScore}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-expand-arrows-alt text-success me-2"></i>比例关系</span>
                            <span class="badge bg-success fs-6" th:text="${score.proportionScore} + '分'">17分</span>
                        </div>
                    </div>
                    
                    <div class="score-item" th:if="${score.lightShadowScore}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-adjust text-warning me-2"></i>明暗关系</span>
                            <span class="badge bg-warning fs-6" th:text="${score.lightShadowScore} + '分'">16分</span>
                        </div>
                    </div>
                    
                    <div class="score-item" th:if="${score.lineQualityScore}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-pencil-alt text-danger me-2"></i>线条质量</span>
                            <span class="badge bg-danger fs-6" th:text="${score.lineQualityScore} + '分'">18分</span>
                        </div>
                    </div>
                    
                    <div class="score-item" th:if="${score.overallEffectScore}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-star text-purple me-2"></i>整体效果</span>
                            <span class="badge bg-dark fs-6" th:text="${score.overallEffectScore} + '分'">16分</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 作品图片 -->
            <div class="col-md-6" th:if="${artwork}">
                <div class="content-section">
                    <h5 class="section-title">
                        <i class="fas fa-image text-primary"></i> 作品图片
                    </h5>
                    
                    <div th:if="${firstImage}" class="text-center">
                        <img th:src="${firstImage}"
                             class="artwork-image"
                             alt="作品图片"
                             onclick="showImageModal(this.src)"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block'">
                        <div class="text-center text-muted py-5" style="display: none;">
                            <i class="fas fa-image fa-3x mb-3"></i>
                            <p>图片加载失败</p>
                        </div>
                    </div>
                    <div th:unless="${firstImage}" class="text-center text-muted py-5">
                        <i class="fas fa-image fa-3x mb-3"></i>
                        <p>暂无图片</p>
                    </div>
                    
                    <div class="mt-3 text-center">
                        <a th:href="@{/admin/artworks/{id}(id=${artwork.id})}" class="btn btn-outline-primary">
                            <i class="fas fa-eye"></i> 查看作品详情
                        </a>
                    </div>
                </div>
            </div>

            <!-- 评价内容 -->
            <div class="col-12">
                <div class="content-section">
                    <h5 class="section-title">
                        <i class="fas fa-comment-dots text-primary"></i> 评价内容
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4" th:if="${evaluation.technicalAnalysis}">
                            <h6><i class="fas fa-search text-info"></i> 技术分析</h6>
                            <p class="text-muted" th:text="${evaluation.technicalAnalysis}">技术分析内容</p>
                        </div>
                        
                        <div class="col-md-6 mb-4" th:if="${evaluation.problemDiagnosis}">
                            <h6><i class="fas fa-exclamation-triangle text-warning"></i> 问题诊断</h6>
                            <p class="text-muted" th:text="${evaluation.problemDiagnosis}">问题诊断内容</p>
                        </div>
                        
                        <div class="col-md-6 mb-4" th:if="${evaluation.improvementPlan}">
                            <h6><i class="fas fa-lightbulb text-success"></i> 改进建议</h6>
                            <p class="text-muted" th:text="${evaluation.improvementPlan}">改进建议内容</p>
                        </div>
                        
                        <div class="col-md-6 mb-4" th:if="${evaluation.stageGoals}">
                            <h6><i class="fas fa-target text-primary"></i> 阶段目标</h6>
                            <p class="text-muted" th:text="${evaluation.stageGoals}">阶段目标内容</p>
                        </div>
                        
                        <div class="col-12" th:if="${evaluation.referenceMaterials}">
                            <h6><i class="fas fa-book text-secondary"></i> 参考资料</h6>
                            <p class="text-muted" th:text="${evaluation.referenceMaterials}">参考资料内容</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="col-12 text-center mb-4">
                <button class="btn btn-outline-danger me-2" onclick="deleteEvaluation()">
                    <i class="fas fa-trash"></i> 删除评价
                </button>
                <a th:href="@{/admin/evaluations}" class="btn btn-primary">
                    <i class="fas fa-list"></i> 返回评价列表
                </a>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="图片预览">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showImageModal(imageSrc) {
            document.getElementById('modalImage').src = imageSrc;
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            modal.show();
        }
        
        function deleteEvaluation() {
            if (confirm('确定要删除这个评价吗？此操作不可恢复！')) {
                const evaluationId = [[${evaluation.id}]];
                
                fetch(`/admin/api/evaluations/${evaluationId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        alert('删除成功');
                        window.location.href = '/admin/evaluations';
                    } else {
                        alert('删除失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    alert('删除失败，请重试');
                });
            }
        }
    </script>
</body>
</html>
