<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户详情 - 美院艺术作品评价系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .user-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        .artwork-thumbnail {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
        }
        .evaluation-badge {
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 返回按钮 -->
            <div class="col-12 mb-3">
                <a href="/admin/users" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回用户列表
                </a>
                <button class="btn btn-outline-secondary ms-2" onclick="history.back()">
                    <i class="fas fa-undo"></i> 返回上页
                </button>
            </div>

            <!-- 用户基本信息 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="user-avatar mb-3 bg-secondary d-flex align-items-center justify-content-center"
                             th:style="${user.avatar != null and !#strings.isEmpty(user.avatar)} ? 'background-image: url(' + ${user.avatar} + '); background-size: cover; background-position: center;' : ''">
                            <i th:unless="${user.avatar != null and !#strings.isEmpty(user.avatar)}" class="fas fa-user fa-3x text-white"></i>
                        </div>
                        <h4 th:text="${user.nickname}">用户昵称</h4>
                        <p class="text-muted mb-2">
                            <i class="fas fa-id-card"></i> ID: <span th:text="${user.id}">1</span>
                        </p>
                        <p class="text-muted mb-2">
                            <i class="fas fa-user-tag"></i> 
                            <span th:if="${user.role == 1}" class="badge bg-primary">学生</span>
                            <span th:if="${user.role == 2}" class="badge bg-success">教师</span>
                        </p>
                        <p class="text-muted mb-3">
                            <i class="fas fa-calendar"></i> 注册时间: 
                            <span th:text="${#temporals.format(user.createdAt, 'yyyy-MM-dd')}">2023-12-01</span>
                        </p>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="toggleEditMode()">
                                <i class="fas fa-edit"></i> 编辑用户
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteUser()">
                                <i class="fas fa-trash"></i> 删除用户
                            </button>
                        </div>

                        <!-- 编辑表单 -->
                        <div id="editForm" class="mt-3" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-edit"></i> 编辑用户信息</h6>
                                </div>
                                <div class="card-body">
                                    <form id="userEditForm">
                                        <input type="hidden" id="userId" th:value="${user.id}">

                                        <div class="mb-3">
                                            <label for="nickname" class="form-label">用户昵称</label>
                                            <input type="text" class="form-control" id="nickname"
                                                   th:value="${user.nickname}" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="role" class="form-label">用户角色</label>
                                            <select class="form-select" id="role" required>
                                                <option value="1" th:selected="${user.role == 1}">学生</option>
                                                <option value="2" th:selected="${user.role == 2}">教师</option>
                                                <option value="3" th:selected="${user.role == 3}">管理员</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="status" class="form-label">账号状态</label>
                                            <select class="form-select" id="status" required>
                                                <option value="1" th:selected="${user.status == 1}">正常</option>
                                                <option value="0" th:selected="${user.status == 0}">禁用</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="avatar" class="form-label">头像URL</label>
                                            <input type="url" class="form-control" id="avatar"
                                                   th:value="${user.avatar}" placeholder="请输入头像URL">
                                        </div>

                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-save"></i> 保存修改
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
                                                <i class="fas fa-times"></i> 取消
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="row">
                    <div class="col-6">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-palette fa-2x me-3"></i>
                                <div>
                                    <h3 class="mb-0" th:text="${totalArtworks}">0</h3>
                                    <small>作品数量</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-card success">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-star fa-2x me-3"></i>
                                <div>
                                    <h3 class="mb-0" th:text="${totalEvaluations}">0</h3>
                                    <small>评价数量</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="stat-card info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-chart-line fa-2x me-3"></i>
                                <div>
                                    <h3 class="mb-0" th:text="${averageScore}">0</h3>
                                    <small>平均评分</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户作品和评价 -->
            <div class="col-md-8">
                <!-- 最近作品 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-palette"></i> 最近作品
                            <span class="badge bg-secondary ms-2" th:text="${totalArtworks}">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(artworks)}" class="text-center text-muted py-4">
                            <i class="fas fa-image fa-3x mb-3"></i>
                            <p>暂无作品</p>
                        </div>
                        
                        <div class="table-responsive" th:unless="${#lists.isEmpty(artworks)}">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>缩略图</th>
                                        <th>标题</th>
                                        <th>状态</th>
                                        <th>上传时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="artwork : ${artworks}">
                                        <td>
                                            <div class="artwork-thumbnail bg-light d-flex align-items-center justify-content-center"
                                                 th:style="${artwork.images != null and !#strings.isEmpty(artwork.images)} ? 'background-image: url(' + ${artwork.images} + '); background-size: cover; background-position: center;' : ''">
                                                <i th:unless="${artwork.images != null and !#strings.isEmpty(artwork.images)}" class="fas fa-image text-muted"></i>
                                            </div>
                                        </td>
                                        <td th:text="${artwork.title}">作品标题</td>
                                        <td>
                                            <span th:if="${artwork.status == 1}" class="badge bg-warning">待评价</span>
                                            <span th:if="${artwork.status == 2}" class="badge bg-success">已评价</span>
                                        </td>
                                        <td th:text="${#temporals.format(artwork.createdAt, 'MM-dd HH:mm')}">12-01 10:00</td>
                                        <td>
                                            <a th:href="@{/admin/artworks/{id}(id=${artwork.id})}" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center mt-3" th:if="${totalArtworks > 10}">
                            <a th:href="@{/admin/artworks?userId={id}(id=${user.id})}" class="btn btn-outline-primary">
                                查看全部作品 <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 最近评价 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star"></i> 最近评价
                            <span class="badge bg-secondary ms-2" th:text="${totalEvaluations}">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(evaluations)}" class="text-center text-muted py-4">
                            <i class="fas fa-comment-slash fa-3x mb-3"></i>
                            <p>暂无评价</p>
                        </div>
                        
                        <div th:unless="${#lists.isEmpty(evaluations)}">
                            <div th:each="evaluation : ${evaluations}" class="border-bottom pb-3 mb-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <span th:if="${evaluation.type == 1}" class="badge bg-success evaluation-badge me-2">
                                                <i class="fas fa-robot"></i> AI评价
                                            </span>
                                            <span th:if="${evaluation.type == 2}" class="badge bg-warning evaluation-badge me-2">
                                                <i class="fas fa-user-tie"></i> 教师评价
                                            </span>
                                            <span th:if="${evaluation.overallScore}" 
                                                  class="badge evaluation-badge"
                                                  th:classappend="${evaluation.overallScore >= 80} ? 'bg-success' : (${evaluation.overallScore >= 60} ? 'bg-warning' : 'bg-danger')"
                                                  th:text="${evaluation.overallScore} + '分'">85分</span>
                                        </div>
                                        <p class="mb-1 text-truncate" th:text="${evaluation.technicalAnalysis}">技术分析内容...</p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> 
                                            <span th:text="${#temporals.format(evaluation.createdAt, 'yyyy-MM-dd HH:mm')}">2023-12-01 10:00</span>
                                        </small>
                                    </div>
                                    <div class="ms-3">
                                        <a th:href="@{/admin/evaluations/{id}(id=${evaluation.id})}" 
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-3" th:if="${totalEvaluations > 10}">
                            <a th:href="@{/admin/evaluations?userId={id}(id=${user.id})}" class="btn btn-outline-primary">
                                查看全部评价 <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        function toggleEditMode() {
            const editForm = document.getElementById('editForm');
            if (editForm.style.display === 'none') {
                editForm.style.display = 'block';
            } else {
                editForm.style.display = 'none';
            }
        }

        function cancelEdit() {
            document.getElementById('editForm').style.display = 'none';
        }

        // 处理表单提交
        document.getElementById('userEditForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const userId = document.getElementById('userId').value;
            const formData = {
                nickname: document.getElementById('nickname').value,
                role: parseInt(document.getElementById('role').value),
                status: parseInt(document.getElementById('status').value),
                avatar: document.getElementById('avatar').value || null
            };

            // 发送更新请求
            $.ajax({
                url: '/admin/api/users/' + userId,
                method: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(response) {
                    if (response.code === 0) {
                        alert('用户信息更新成功！');
                        location.reload(); // 刷新页面显示最新数据
                    } else {
                        alert('更新失败: ' + response.msg);
                    }
                },
                error: function(xhr, status, error) {
                    alert('更新失败，请重试');
                    console.error('Error:', error);
                }
            });
        });

        function deleteUser() {
            if (confirm('确定要删除该用户吗？此操作不可恢复！')) {
                const userId = document.querySelector('#userId').value;

                $.ajax({
                    url: '/admin/api/users/' + userId,
                    method: 'DELETE',
                    success: function(response) {
                        if (response.code === 0) {
                            alert('删除成功');
                            window.location.href = '/admin/users';
                        } else {
                            alert('删除失败: ' + response.msg);
                        }
                    },
                    error: function() {
                        alert('删除失败，请重试');
                    }
                });
            }
        }
    </script>
</body>
</html>
