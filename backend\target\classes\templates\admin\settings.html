<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 美院作品评价管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --sidebar-width: 250px;
            --header-height: 60px;
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: block;
            padding: 15px 25px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: var(--accent-color);
        }

        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }

        .header {
            background: white;
            height: var(--header-height);
            padding: 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .content {
            padding: 30px;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #eee;
            font-weight: 600;
        }

        .page-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 30px;
        }

        .setting-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            font-weight: 600;
            color: var(--primary-color);
        }

        .setting-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>管理系统</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="/admin"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/users"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/artworks"><i class="fas fa-palette"></i> 作品管理</a></li>
            <li><a href="/admin/evaluations"><i class="fas fa-star"></i> 评价管理</a></li>
            <li><a href="/admin/teacher-applications"><i class="fas fa-user-graduate"></i> 教师认证</a></li>
            <li><a href="/admin/settings" class="active"><i class="fas fa-cog"></i> 系统设置</a></li>
        </ul>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 头部 -->
        <div class="header">
            <h5 class="mb-0">系统设置</h5>
            <div class="d-flex align-items-center">
                <span class="text-muted me-3">美院作品评价管理系统</span>
                <a href="/admin/logout" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <h2 class="page-title">系统设置</h2>

            <!-- 系统统计 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h3 th:text="${totalUsers}">0</h3>
                            <p class="text-muted mb-0">注册用户</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-palette fa-2x text-success mb-2"></i>
                            <h3 th:text="${totalArtworks}">0</h3>
                            <p class="text-muted mb-0">上传作品</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-star fa-2x text-warning mb-2"></i>
                            <h3 th:text="${totalEvaluations}">0</h3>
                            <p class="text-muted mb-0">评价记录</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="setting-item">
                                <div class="d-flex justify-content-between">
                                    <span class="setting-label">系统版本</span>
                                    <span th:text="${systemVersion}">1.0.0</span>
                                </div>
                            </div>
                            <div class="setting-item">
                                <div class="d-flex justify-content-between">
                                    <span class="setting-label">Java版本</span>
                                    <span th:text="${javaVersion}">17.0.1</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="setting-item">
                                <div class="d-flex justify-content-between">
                                    <span class="setting-label">操作系统</span>
                                    <span th:text="${osName}">Windows 11</span>
                                </div>
                            </div>
                            <div class="setting-item">
                                <div class="d-flex justify-content-between">
                                    <span class="setting-label">运行状态</span>
                                    <span class="badge bg-success">正常运行</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 基础设置 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>基础设置</h5>
                </div>
                <div class="card-body">
                    <div class="setting-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="setting-label">系统名称</div>
                                <div class="setting-description">设置系统显示名称</div>
                            </div>
                            <div>
                                <input type="text" class="form-control" value="美院作品评价管理系统" style="width: 300px;">
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="setting-label">开发模式</div>
                                <div class="setting-description">开启后使用模拟登录，关闭后使用真实微信登录</div>
                            </div>
                            <div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="devMode" checked>
                                    <label class="form-check-label" for="devMode">启用</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="setting-label">文件上传大小限制</div>
                                <div class="setting-description">单个文件最大上传大小（MB）</div>
                            </div>
                            <div>
                                <input type="number" class="form-control" value="10" min="1" max="100" style="width: 100px;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 微信配置 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fab fa-weixin me-2"></i>微信小程序配置</h5>
                </div>
                <div class="card-body">
                    <div class="setting-item">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="setting-label">AppID</div>
                                <div class="setting-description">微信小程序AppID</div>
                            </div>
                            <div class="col-md-9">
                                <input type="text" class="form-control" value="wx33a5eb34e3b685fc" placeholder="请输入微信小程序AppID">
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="setting-label">AppSecret</div>
                                <div class="setting-description">微信小程序AppSecret</div>
                            </div>
                            <div class="col-md-9">
                                <input type="password" class="form-control" value="9edd1e97f3faf268eaf487d66b0e9ba9" placeholder="请输入微信小程序AppSecret">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI配置 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-robot me-2"></i>AI评价配置</h5>
                </div>
                <div class="card-body">
                    <div class="setting-item">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="setting-label">火山方舟API Key</div>
                                <div class="setting-description">火山方舟平台API密钥</div>
                            </div>
                            <div class="col-md-9">
                                <input type="password" class="form-control" value="your-ark-api-key" placeholder="请输入火山方舟API Key">
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="setting-label">接入点地址</div>
                                <div class="setting-description">火山方舟推理接入点地址</div>
                            </div>
                            <div class="col-md-9">
                                <input type="text" class="form-control" value="https://ark.cn-beijing.volces.com/api/v3" placeholder="请输入接入点地址">
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="setting-label">AI模型</div>
                                <div class="setting-description">使用的AI模型端点ID</div>
                            </div>
                            <div class="col-md-9">
                                <input type="text" class="form-control" value="ep-20241128140423-8xvpz" placeholder="请输入模型端点ID">
                                <small class="form-text text-muted">
                                    模型端点ID可在火山方舟控制台的推理接入点页面获取
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="text-end">
                <button type="button" class="btn btn-secondary me-2">重置</button>
                <button type="button" class="btn btn-primary" onclick="saveSettings()">保存设置</button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        function saveSettings() {
            // 这里可以添加保存设置的逻辑
            alert('设置保存功能开发中...');
        }
    </script>
</body>
</html>
