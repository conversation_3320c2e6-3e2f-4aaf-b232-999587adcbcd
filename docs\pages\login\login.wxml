<t-navbar title="登录" left-arrow />
<view class="login-container">
  <view class="login-header">
    <view class="logo">
      <t-icon name="palette" size="80rpx" color="white" />
    </view>
    <text class="app-name">美院作品评价</text>
    <text class="app-desc">专业素描评价系统</text>
  </view>

  <view class="login-content">
    <view class="feature-list">
      <view class="feature-item">
        <t-icon name="upload" size="32rpx" color="#667eea" />
        <text>上传作品获得专业评价</text>
      </view>
      <view class="feature-item">
        <t-icon name="chart-bubble" size="32rpx" color="#667eea" />
        <text>AI智能分析技术要点</text>
      </view>
      <view class="feature-item">
        <t-icon name="star" size="32rpx" color="#667eea" />
        <text>量化评分系统</text>
      </view>
      <view class="feature-item">
        <t-icon name="book" size="32rpx" color="#667eea" />
        <text>个性化改进建议</text>
      </view>
    </view>

    <view class="login-buttons">
      <t-button
        theme="primary"
        size="large"
        block
        class="wx-login-btn"
        style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; color: white !important; border: none !important;"
        bindtap="wxLogin"
        loading="{{logging}}"
      >
        <t-icon name="logo-wechat" size="40rpx" />
        微信一键登录
      </t-button>

      <t-button
        theme="default"
        size="large"
        block
        style="margin-top: 20rpx;"
        bindtap="testApi"
        loading="{{testing}}"
      >
        测试API连接
      </t-button>

      <view class="login-tips">
        <text>点击登录即表示同意</text>
        <text class="link">《用户协议》</text>
        <text>和</text>
        <text class="link">《隐私政策》</text>
      </view>
    </view>
  </view>
</view>

<t-toast id="t-toast" />
